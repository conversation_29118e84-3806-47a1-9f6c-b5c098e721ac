enum EGameAction {
  // outbound
  connect('connect'),
  initiate('initiate'),
  startGame('startGame'),
  cancelGame('cancelGame'),
  addBot('addBot'),
  addBots('addBots'),
  disconnectEndGame('disconnectEndGame'),
  setPlayerAnswer('setPlayerAnswer'),
  setPlayerPontuation('setPlayerPontuation'),
  disconnect('disconnect'),
  chat('chat'),
  // other
  changeState('changeState'),
  // inbound
  welcome('welcome'),
  gameStarting('gameStarting'),
  failure('failure'),
  userEntered('userEntered'),
  userLeft('userLeft'),
  countDown('countDown'),
  isCompleted('isCompleted'),
  gameCancelled('gameCancelled'),
  startRound('startRound'),
  updateAnswers('updateAnswers'),
  updatePontuations('updatePontuations'),
  reviewRound('reviewRound'),
  playerReceivePontuation('playerReceivePontuation'),
  endGame('endGame'),
  endGameUpdateData('endGameUpdateData'),
  // chat inbound actions
  conversationList('conversationList'),
  messagesBetweenPlayers('messagesBetweenPlayers'),
  messageList('messageList'),
  messageSent('messageSent'),
  messageReceived('messageReceived'),
  messagesUpdated('messagesUpdated'),
  conversationsUpdated('conversationsUpdated'),
  countAsRead('countAsRead'),
  unreadCountUpdated('unreadCountUpdated');

  final String label;

  @override
  String toString() {
    return label;
  }

  const EGameAction(this.label);

  static EGameAction fromString(String label) {
    return values.firstWhere(
      (v) => v.label == label,
      orElse: () => EGameAction.endGame,
    );
  }
}
