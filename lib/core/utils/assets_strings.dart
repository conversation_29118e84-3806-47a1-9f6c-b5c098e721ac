class _SvgMatchBackgroundsAssets {
  final String customPath;
  final String format;

  _SvgMatchBackgroundsAssets({required this.customPath, required this.format});

  String operator [](String assetName) {
    return '$customPath/background/$assetName.$format';
  }

  String call(String varName) {
    switch (varName) {
      case "van":
        return this["van"];
      case "rom":
        return this["rom"];
      case "mp":
        return this["mp"];
      case "trl":
        return this["trl"];
      case "spo":
        return this["spo"];
      case "foc":
        return this["foc"];
      case "cl":
        return this["cl"];
      case "res":
        return this["res"];
      case "spr":
        return this["spr"];
      case "kin":
        return this["kin"];
      default:
        return '';
    }
  }

  String getByName(String name) {
    return this(name);
  }

  String get van => this["van"];
  String get rom => this["rom"];
  String get mp => this["mp"];
  String get trl => this["trl"];
  String get spo => this["spo"];
  String get foc => this["foc"];
  String get cl => this["cl"];
  String get res => this["res"];
  String get spr => this["spr"];
  String get kin => this["kin"];
}

class _SvgOverlayAssets {
  final String customPath;
  final String format;

  _SvgOverlayAssets({required this.customPath, required this.format});

  String operator [](String assetName) {
    return '$customPath/overlay/$assetName.$format';
  }

  String get playMoreToUnlock => this["play_more_to_unlock"];
  String get expired => this["expired"];
  String get prizeClaimed => this["prize_claimed"];
  String get playMoreToUnlickVibeCheck =>
      this["play_more_to_unlock_vibe_check"];
}

class _SvgIQAssets {
  final String customPath;
  final String format;

  _SvgIQAssets({required this.customPath, required this.format});

  String operator [](String assetName) {
    return '$customPath/iq_icons/$assetName.$format';
  }

  String call(String varName) {
    switch (varName) {
      case "van":
        return this["van"];
      case "rom":
        return this["rom"];
      case "mp":
        return this["mp"];
      case "trl":
        return this["trl"];
      case "spo":
        return this["spo"];
      case "foc":
        return this["foc"];
      case "cl":
        return this["cl"];
      case "res":
        return this["res"];
      case "spr":
        return this["spr"];
      case "kin":
        return this["kin"];
      case "vanDisabled":
        return this["vanDisabled"];
      case "romDisabled":
        return this["romDisabled"];
      case "mpDisabled":
        return this["mpDisabled"];
      case "trlDisabled":
        return this["trlDisabled"];
      case "spoDisabled":
        return this["spoDisabled"];
      case "focDisabled":
        return this["focDisabled"];
      case "clDisabled":
        return this["clDisabled"];
      case "resDisabled":
        return this["resDisabled"];
      case "sprDisabled":
        return this["sprDisabled"];
      case "kinDisabled":
        return this["kinDisabled"];
      default:
        return '';
    }
  }

  String getByName(String name) {
    return this(name);
  }

  String get van => this["van"];
  String get rom => this["rom"];
  String get mp => this["mp"];
  String get trl => this["trl"];
  String get spo => this["spo"];
  String get foc => this["foc"];
  String get cl => this["cl"];
  String get res => this["res"];
  String get spr => this["spr"];
  String get kin => this["kin"];
  String get vanDisabled => this["van_disabled"];
  String get romDisabled => this["rom_disabled"];
  String get mpDisabled => this["mp_disabled"];
  String get trlDisabled => this["trl_disabled"];
  String get spoDisabled => this["spo_disabled"];
  String get focDisabled => this["foc_disabled"];
  String get clDisabled => this["cl_disabled"];
  String get resDisabled => this["res_disabled"];
  String get sprDisabled => this["spr_disabled"];
  String get kinDisabled => this["kin_disabled"];
}

class _SvgAssets {
  final String customPath;
  final String format;

  late final _SvgOverlayAssets overlay;
  late final _SvgIQAssets iq;
  late final _SvgMatchBackgroundsAssets backgrounds;

  _SvgAssets({required this.customPath, required this.format}) {
    overlay = _SvgOverlayAssets(customPath: customPath, format: format);
    iq = _SvgIQAssets(customPath: customPath, format: format);
    backgrounds =
        _SvgMatchBackgroundsAssets(customPath: customPath, format: format);
  }

  String operator [](String assetName) {
    return '$customPath/$assetName.$format';
  }

  String get avatar => this["avatar"];
  String get quyckyLogo => this["quycky_logo"];
  String get quyckyLogoCharmPink => this["quycky_logo_charm_pink"];
  String get quyckyLogoWhite => this["quycky_logo_white"];
  String get quyckyWelcomeLogoWhite => this["quycky_welcome_logo_white"];
  String get quyckyLogoWhiteDefault => this["quycky_logo_white_default"];
  String get image => this["image"];
  String get camera => this["camera"];
  String get quyckyPin => this["quycky_pin"];
  String get quyckyPinFire => this["quycky_pin_fire"];
  String get googleLogo => this["google_logo"];
  String get appleLogo => this["apple_logo"];
  String get stageFrame => this["stage_frame"];
  String get fire => this["fire"];
  String get fire2 => this["fire_2"];
  String get fire3 => this["fire_3"];
  String get logo => this["logo"];
  String get close => this["close"];
  String get uploadFile => this["upload_file"];
  String get addFriendButton => this["add_friend_button"];
  String get quyckyLogotype => this["quycky_logotype"];
  String get lock => this["lock"];
  String get padlockWithCircle => this["padlock_with_circle"];
}

class _PngAssets {
  final String customPath;
  final String format;

  _PngAssets({required this.customPath, required this.format});

  String operator [](String assetName) {
    return '$customPath/$assetName.$format';
  }

  String get googleLogo => this["google_logo"];
  String get appleLogo => this["apple_logo"];
  String get quyckyWelcomeLogoWhite => this["quycky_welcome_logo_white"];
  String get fire => this["fire"];
  String get circles => this['circles'];
  String get ice => this["ice"];
  String get avatar => this["avatar"];
  String get intro1 => this["intro/intro1"];
  String get intro2 => this["intro/intro2"];
  String get intro3 => this["intro/intro3"];
  String get intro3Android => this["intro/intro3_android"];
  String get intro4 => this["intro/intro4"];
  String get profileTest => this["profile_test"];
  String get circlesRadar => this["circles_radar"];
}

class _LottieAssets {
  final String customPath;
  final String format;

  _LottieAssets({required this.customPath, required this.format});

  String operator [](String assetName) {
    return '$customPath/$assetName.$format';
  }

  String get circles => this["circles"];
  String get flashing => this["flashing"];
}

class _RivAssets {
  final String customPath;
  final String format;

  _RivAssets({required this.customPath, required this.format});

  String operator [](String assetName) {
    return '$customPath/$assetName.$format';
  }

  String get gift_box => this["gift_box"];
}

class Assets {
  static const String _customPath = 'assets';
  Assets._();
  static const envFile = ".env";
  static final svg =
      _SvgAssets(customPath: "$_customPath/img/svg", format: 'svg');
  static final png =
      _PngAssets(customPath: "$_customPath/img/png", format: 'png');
  static final lottie =
      _LottieAssets(customPath: "$_customPath/animations", format: 'json');
  static final riv =
      _RivAssets(customPath: "$_customPath/animations/rive", format: 'riv');
}
