// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:quycky/core/utils/type_converters.dart';

class QuestionOptionEntity extends Equatable {
  final int id;
  final String value;
  final int questionId;
  final String questionsOptionsTags;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String status;
  final bool isCorrectAnswer;
  final String imageUrl;

  const QuestionOptionEntity({
    required this.id,
    required this.value,
    required this.questionId,
    required this.questionsOptionsTags,
    required this.createdAt,
    required this.updatedAt,
    required this.status,
    required this.isCorrectAnswer,
    required this.imageUrl,
  });

  QuestionOptionEntity copyWith({
    int? id,
    String? value,
    int? questionId,
    String? questionsOptionsTags,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? status,
    bool? isCorrectAnswer,
    String? imageUrl,
  }) {
    return QuestionOptionEntity(
      id: id ?? this.id,
      value: value ?? this.value,
      questionId: questionId ?? this.questionId,
      questionsOptionsTags: questionsOptionsTags ?? this.questionsOptionsTags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      status: status ?? this.status,
      isCorrectAnswer: isCorrectAnswer ?? this.isCorrectAnswer,
      imageUrl: imageUrl ?? this.imageUrl,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'value': value,
      'question_id': questionId,
      'questions_options_tags': questionsOptionsTags,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
      'status': status,
      'is_correct_answer': isCorrectAnswer ? 1 : 0,
      'image_url': imageUrl,
    };
  }

  factory QuestionOptionEntity.fromMap(Map<String, dynamic> map) {
    var olderDate = DateTime.fromMillisecondsSinceEpoch(1);
    return QuestionOptionEntity(
        id: dynamicToInt(map['id']),
        value: map['value'] as String,
        questionId: dynamicToInt(map['question_id']),
        questionsOptionsTags: map['questions_options_tags'] as String,
        createdAt: map['created_at'].runtimeType != Null
            ? DateTime.parse(map['created_at'])
            : olderDate,
        updatedAt: map['updated_at'].runtimeType != Null
            ? DateTime.parse(map['updated_at'])
            : olderDate,
        status: map['status'] as String,
        imageUrl: (map['image_url'] ?? '') as String,
        isCorrectAnswer: dynamicToBoolean(map['is_correct_answer']));
  }

  String toJson() => json.encode(toMap());

  factory QuestionOptionEntity.fromJson(String source) =>
      QuestionOptionEntity.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  bool get stringify => true;

  @override
  List<Object> get props {
    return [
      id,
      value,
      questionId,
      questionsOptionsTags,
      createdAt,
      updatedAt,
      status,
      isCorrectAnswer,
      imageUrl,
    ];
  }
}
