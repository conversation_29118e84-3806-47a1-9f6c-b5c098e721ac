// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:quycky/core/entities/abs_mappable.dart';
import 'package:quycky/core/utils/type_converters.dart';

// TODO: Importar ou definir GamePointTypeEnumerator
// import 'path/to/game_point_type_enumerator.dart';

class PlayerSendPontuationDto implements AbsMappable {
  final String roomName;
  final int round;
  final String userThatSetPontuationIdentifier;
  final String userThatReceivePontuationIdentifier;
  final int pontuation;
  final int? questionId;

  PlayerSendPontuationDto({
    required this.roomName,
    required this.round,
    required this.userThatSetPontuationIdentifier,
    required this.userThatReceivePontuationIdentifier,
    required this.pontuation,
    this.questionId,
  });

  PlayerSendPontuationDto copyWith({
    String? roomName,
    int? round,
    String? userThatSetPontuationIdentifier,
    String? userThatReceivePontuationIdentifier,
    int? pontuation,
    int? questionId,
  }) {
    return PlayerSendPontuationDto(
      roomName: roomName ?? this.roomName,
      round: round ?? this.round,
      userThatSetPontuationIdentifier: userThatSetPontuationIdentifier ??
          this.userThatSetPontuationIdentifier,
      userThatReceivePontuationIdentifier:
          userThatReceivePontuationIdentifier ??
              this.userThatReceivePontuationIdentifier,
      pontuation: pontuation ?? this.pontuation,
      questionId: questionId ?? this.questionId,
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'roomName': roomName,
      'round': round,
      'userThatSetPontuationIdentifier': userThatSetPontuationIdentifier,
      'userThatReceivePontuationIdentifier':
          userThatReceivePontuationIdentifier,
      'pontuation': pontuation,
      'questionId': questionId,
    };
  }

  factory PlayerSendPontuationDto.fromJson(String source) =>
      PlayerSendPontuationDto.fromMap(
          json.decode(source) as Map<String, dynamic>);

  factory PlayerSendPontuationDto.fromMap(Map<String, dynamic> map) {
    return PlayerSendPontuationDto(
      roomName: map['roomName'] as String,
      round: map['round'] as int,
      userThatSetPontuationIdentifier:
          map['userThatSetPontuationIdentifier'] as String,
      userThatReceivePontuationIdentifier:
          map['userThatReceivePontuationIdentifier'] as String,
      pontuation: dynamicToInt(map['pontuation']),
      questionId: map['questionId'] != null ? map['questionId'] as int : null,
    );
  }

  @override
  String toJson() => json.encode(toMap());

  @override
  String toString() {
    return 'PlayerSendPontuationDto(roomName: $roomName, round: $round, userThatSetPontuationIdentifier: $userThatSetPontuationIdentifier, userThatReceivePontuationIdentifier: $userThatReceivePontuationIdentifier, pontuation: $pontuation, questionId: $questionId)';
  }

  @override
  bool operator ==(covariant PlayerSendPontuationDto other) {
    if (identical(this, other)) return true;

    return other.roomName == roomName &&
        other.round == round &&
        other.userThatSetPontuationIdentifier ==
            userThatSetPontuationIdentifier &&
        other.userThatReceivePontuationIdentifier ==
            userThatReceivePontuationIdentifier &&
        other.pontuation == pontuation &&
        other.questionId == questionId;
  }

  @override
  int get hashCode {
    return roomName.hashCode ^
        round.hashCode ^
        userThatSetPontuationIdentifier.hashCode ^
        userThatReceivePontuationIdentifier.hashCode ^
        pontuation.hashCode ^
        questionId.hashCode;
  }
}
