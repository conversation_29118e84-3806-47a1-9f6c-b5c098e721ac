import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:quycky/app/features/game/domain/entities/game_entity.dart';
import 'package:quycky/app/features/game/domain/entities/socket_user_entity.dart';
import 'package:quycky/app/features/game/presenter/pages/widgets/game_player_response_item.dart';
import 'package:quycky/app/features/user/domain/entities/user_entity.dart';
import 'package:quycky/core/utils/app_env.dart';
import 'package:sizer/sizer.dart';

TextStyle buttonDefaultTextStyle(Color color, {double fontSize = 15}) =>
    TextStyle(
        letterSpacing: 3,
        fontFamily: "Roboto",
        fontWeight: FontWeight.bold,
        color: color,
        fontSize: fontSize);

class GamePlayersResponsesSection extends StatefulWidget {
  final String question;
  final List<SocketUserEntity> players;
  final UserEntity localPlayer;
  final bool alreadyGavePoints;
  final void Function(String identifier, EGamePontuationType pontuationType)
      givePointToPlayer;
  final void Function() hidePointButtons;
  final int Function(String identifier) getPlayerTotalPointsByIdentifier;
  final String Function(String identifier) getPlayerCurrentAnswerByIdentifier;
  final List<EGamePontuationType>? receivedPontuations;
  final int Function(String identifier) getPlayerTotalFiresByIdentifier;
  final String userWhoReceivedPoint;
  final EGamePontuationType receivedPointType;
  final bool isReviewingMode;
  final Uint8List? Function(String id) getPlayerImageMemoryData;
  final bool showPointButtons;

  const GamePlayersResponsesSection(
      {super.key,
      required this.getPlayerImageMemoryData,
      required this.question,
      required this.players,
      required this.givePointToPlayer,
      required this.hidePointButtons,
      required this.localPlayer,
      required this.alreadyGavePoints,
      required this.getPlayerCurrentAnswerByIdentifier,
      required this.showPointButtons,
      this.receivedPontuations,
      this.userWhoReceivedPoint = '',
      this.receivedPointType = EGamePontuationType.cold,
      this.isReviewingMode = false,
      required this.getPlayerTotalPointsByIdentifier,
      required this.getPlayerTotalFiresByIdentifier});

  @override
  State<GamePlayersResponsesSection> createState() =>
      _GamePlayersResponsesSection();
}

class _GamePlayersResponsesSection extends State<GamePlayersResponsesSection> {
  final _defaultPlayer = [
    const UserEntity(name: 'ROB'),
    const UserEntity(name: 'NICK_32'),
    const UserEntity(name: 'AISHA')
  ];
  bool isLessOrEqualsToMinimumLayoutHeight = false;
  UserEntity getPlayer(int index) {
    if (!widget.players.asMap().containsKey(index)) {
      return _defaultPlayer[index];
    }
    return widget.players[index].user;
  }

  Widget getPlayerResponseItem(UserEntity player) {
    //int index) {
    // UserEntity player = getPlayer(index);
    String identifier = player.identifier ?? '';

    return GamePlayerResponseItem(
        player: player,
        isReviewingMode: widget.isReviewingMode,
        playerImageMemoryData: widget.getPlayerImageMemoryData(player.id),
        userWhoReceivedPoint: widget.userWhoReceivedPoint,
        receivedPointType: widget.receivedPointType,
        alreadyGavePoints: widget.alreadyGavePoints,
        points: widget.getPlayerTotalPointsByIdentifier(identifier),
        givePointToPlayer: widget.givePointToPlayer,
        hidePointButtons: widget.hidePointButtons,
        answer: widget.getPlayerCurrentAnswerByIdentifier(identifier),
        showPointButtons: widget.showPointButtons,
        receivedPontuations:
            widget.getPlayerTotalFiresByIdentifier(identifier));
  }

  List<Widget> getPlayersResponseItems() {
    List<Widget> res = [];
    for (var element in widget.players) {
      res.add(getPlayerResponseItem(element.user));
      res.add(SizedBox(
        height: 1.h,
      ));
    }
    return res;
  }

  Widget getLocalPlayerResponseItem() {
    String identifier = widget.localPlayer.identifier ?? '';
    final double avatarSize =
        7.7.h; //isLessOrEqualsToMinimumLayoutHeight ? 55 : 65;
    return GamePlayerResponseItem(
        player: widget.localPlayer, // ?? const UserEntity(name: 'ME'),
        playerImageMemoryData:
            widget.getPlayerImageMemoryData(widget.localPlayer.id),
        showAddButton: false,
        isLocalPlayer: true,
        hidePointButtons: widget.hidePointButtons,
        showPointButtons: widget.showPointButtons,
        avatarSize: avatarSize,
        alreadyGavePoints: widget.alreadyGavePoints,
        points: widget.getPlayerTotalPointsByIdentifier(identifier),
        answer: widget.getPlayerCurrentAnswerByIdentifier(identifier),
        receivedPontuations:
            widget.getPlayerTotalFiresByIdentifier(identifier));
  }

  Widget getWidget() => Column(children: [
        Container(
          constraints: BoxConstraints(maxWidth: 89.w),
          height: 9.5.h,
          margin: EdgeInsets.symmetric(vertical: 1.77.h),
          child: Text(
            widget.question,
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
            maxLines: 3,
            style: TextStyle(
                fontFamily: "Roboto",
                height: 1.1,
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 21),
          ),
        ),
        ...getPlayersResponseItems(),
        Container(child: getLocalPlayerResponseItem()),
      ]);

  @override
  Widget build(BuildContext context) {
    isLessOrEqualsToMinimumLayoutHeight =
        MediaQuery.of(context).size.height <= AppEnv.minimumLayoutHeight;
    return getWidget();
  }
}
