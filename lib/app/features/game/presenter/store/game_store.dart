import 'dart:typed_data';

import 'package:flutter_triple/flutter_triple.dart';
import 'package:quycky/app/features/game/domain/entities/answer_entity.dart';
import 'package:quycky/app/features/game/domain/entities/game_entity.dart';
import 'package:quycky/app/features/game/domain/entities/pontuation_entity.dart';
import 'package:quycky/app/features/game/domain/entities/question_entity.dart';
import 'package:quycky/app/features/game/domain/entities/question_option_entity.dart';
import 'package:quycky/app/features/game/domain/entities/room_entity.dart';
import 'package:quycky/app/features/game/domain/entities/socket_user_entity.dart';
import 'package:quycky/app/features/game/domain/entities/user_image_memory_data.dart';
import 'package:quycky/core/usecase/errors/failure.dart';

class GameStore extends Store<GameEntity> {
  GameStore()
      : super(const GameEntity(
            numberOfGamesPlayedByLoggedUser: 0,
            invitedPlayers: {},
            gameStartStage: EGameStartStage.none,
            showAllInviteSlots: false,
            receivedPontuations: [],
            gameMode: EGameMode.fourRandom,
            currentState: EGameState.waitingPlayers,
            receivedPontuationType: EGamePontuationType.cold,
            userWhoReceivedPoint: '',
            room: RoomEntity(
                answers: [],
                flagCurrentRound: 0,
                flagPlayersFinished: 0,
                flagPlayersReady: 0,
                pontuations: [],
                questions: [],
                roomName: '',
                flagMaxPlayers: 4,
                users: [],
                numRounds: 6,
                gameState: EGameState.waitingPlayers,
                gameMode: EGameMode.fourRandom)));

  setIsRoomOwner(bool isRoomOwner) {
    update(state.copyWith(isRoomOwner: isRoomOwner), force: true);
  }

  Uint8List? getFriendImageDataById(String id) {
    Uint8List? res;
    for (int i = 0; i < state.friendsImages.length; i++) {
      if (state.friendsImages[i].id == id) {
        res = state.friendsImages[i].imageData;
        break;
      }
    }
    return res;
  }

  addFriendImage(UserImageMemoryData data) {
    if (getFriendImageDataById(data.id) == null) {
      state.friendsImages.add(data);
      update(state.copyWith(friendsImages: state.friendsImages));
    }
  }

  addFriendsImages(List<UserImageMemoryData> data) {
    update(state.copyWith(friendsImages: data), force: true);
  }

  setCurrentState(EGameState newState) {
    update(state.copyWith(currentState: newState), force: true);
  }

  setGameStartStage(EGameStartStage newState) {
    update(state.copyWith(gameStartStage: newState), force: true);
  }

  setGameMode(EGameMode gameMode) =>
      update(state.copyWith(gameMode: gameMode), force: true);

  setAnswers(List<AnswerEntity> listAnswers) {
    update(state.copyWith(room: state.room.copyWith(answers: listAnswers)),
        force: true);
  }

  setPontuations(List<PontuationEntity> listPontuations) {
    update(
        state.copyWith(room: state.room.copyWith(pontuations: listPontuations)),
        force: true);
  }

  setAlreadyPlayed(bool played) {
    update(state.copyWith(alreadyPlayed: played));
  }

  addInvitedPlayer(int pos, SocketUserEntity socketUser) {
    var invitedPlayers = Map<int, SocketUserEntity>.from(state.invitedPlayers);
    invitedPlayers[pos] = socketUser;
    update(state.copyWith(invitedPlayers: invitedPlayers), force: true);
  }

  changeInvitedUser(int pos, SocketUserEntity socketUser) {
    var invitedPlayers = Map<int, SocketUserEntity>.from(state.invitedPlayers);
    invitedPlayers[pos] = socketUser;
    update(state.copyWith(invitedPlayers: invitedPlayers), force: true);
  }

  setShowAllInviteSlots(bool value) {
    update(state.copyWith(showAllInviteSlots: value), force: true);
  }

  removeInvitedPlayer(int pos) {
    var invitedPlayers = {...state.invitedPlayers};
    invitedPlayers.remove(pos);
    update(state.copyWith(invitedPlayers: invitedPlayers), force: true);
  }

  clearInvitedPlayers() {
    update(state.copyWith(invitedPlayers: {}));
  }

  clear() {
    update(
        const GameEntity(
            receivedPontuations: [],
            showPointButtons: true,
            invitedPlayers: {},
            gameMode: EGameMode.fourRandom,
            currentState: EGameState.waitingPlayers,
            receivedPontuationType: EGamePontuationType.cold,
            userWhoReceivedPoint: '',
            selectedQuestionOption: null,
            room: RoomEntity(
              answers: [],
              flagCurrentRound: 0,
              flagPlayersFinished: 0,
              flagPlayersReady: 0,
              pontuations: [],
              flagMaxPlayers: 4,
              questions: [],
              roomName: '',
              stateTimeInSeconds: 0,
              timeAnsweringRound: 0,
              timeReviewingRound: 0,
              timeWaitingStartingGame: 0,
              users: [],
              gameMode: EGameMode.fourRandom,
              gameState: EGameState.waitingPlayers,
            )),
        force: true);
  }

  QuestionEntity get currentQuestion =>
      state.room.questions.isNotEmpty && state.room.flagCurrentRound > 0
          ? state.room.questions[state.room.flagCurrentRound - 1]
          : QuestionEntity(
              id: 0,
              description: 'No question',
              isActived: 1,
              stage: 1,
              type: 'direct_answer',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
              questionsOptions: const []);

  setSelectedQuestionOption(QuestionOptionEntity? newValue) {
    update(
        state.copyWith(
            selectedQuestionOption: newValue,
            updateSelectedQuestionOption: true),
        force: true);
  }

  setNumberOfGamesPlayedByLoggedUser(int newValue) {
    update(state.copyWith(numberOfGamesPlayedByLoggedUser: newValue),
        force: true);
  }

  setStateTimeInSeconds(int val) {
    update(state.copyWith(room: state.room.copyWith(stateTimeInSeconds: val)));
  }

  setRoomData(RoomEntity room) {
    update(state.copyWith(room: room), force: true);
  }

  setAlreadyGavePoint(bool gavePoint,
      {String userWhoReceivedPoint = '',
      EGamePontuationType pontuationType = EGamePontuationType.cold}) {
    update(
        state.copyWith(
            alreadyGavePoint: gavePoint,
            receivedPontuationType: pontuationType,
            userWhoReceivedPoint: userWhoReceivedPoint),
        force: true);
  }

  setShowPointButtons(bool value) =>
      update(state.copyWith(showPointButtons: value), force: true);

  clearReceivedPontuations() {
    update(state.copyWith(receivedPontuations: []), force: true);
  }

  addReceivedPontuations(PontuationEntity pontuation) {
    List<EGamePontuationType> receivedPontuations = state.receivedPontuations;
    receivedPontuations.add(pontuation.pontuation == 10
        ? EGamePontuationType.fire
        : EGamePontuationType.cold);
    List<PontuationEntity> pontuationsEntity = state.room.pontuations;
    pontuationsEntity.add(pontuation);
    update(
        state.copyWith(
            receivedPontuations: receivedPontuations,
            room: state.room.copyWith(pontuations: pontuationsEntity)),
        force: true);
  }

  addRoomPontuation(PontuationEntity pontuation) {
    List<PontuationEntity> pontuationsEntity = state.room.pontuations;
    pontuationsEntity.add(pontuation);
    update(
        state.copyWith(
            room: state.room.copyWith(pontuations: pontuationsEntity)),
        force: true);
  }

  setRoomUsers(List<SocketUserEntity> usersData) {
    update(state.copyWith(room: state.room.copyWith(users: usersData)),
        force: true);
  }

  setErrorData(Failure error) {
    setError(error);
  }
}
