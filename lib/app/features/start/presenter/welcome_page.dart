import 'dart:io';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_triple/flutter_triple.dart';
import 'package:quycky/analytics.dart';
import 'package:quycky/app/features/start/presenter/widgets/welcome_page_animated_background.dart';
import 'package:quycky/app/features/user/domain/entities/user_login_return_entity.dart';
import 'package:quycky/app/features/user/presenter/controllers/user_controller.dart';
import 'package:quycky/app/features/user/presenter/pages/user_register_page.dart';
import 'package:quycky/app/features/user/presenter/storage/user_storage.dart';
import 'package:quycky/app/features/user/presenter/store/user_store.dart';
import 'package:quycky/app/services/remote_config/remote_config_service.dart';
import 'package:quycky/app/services/remote_config/remote_config_store.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/custom_image.dart';
import 'package:quycky/core/usecase/errors/failure.dart';
import 'package:quycky/core/utils/app_env.dart';
import 'package:quycky/core/utils/app_routes.dart';
import 'package:quycky/app/widgets/button.dart';
import 'package:quycky/core/utils/assets_strings.dart';
import 'package:quycky/core/utils/interval_utils.dart';
import 'package:quycky/core/utils/show_message.dart';
import 'package:quycky/core/utils/social_sign_in.dart';
import 'package:quycky/core/utils/type_converters.dart';
import 'package:url_launcher/url_launcher.dart';

class WelcomePage extends StatefulWidget {
  final String title;

  const WelcomePage({super.key, this.title = 'WelcomePage'});

  @override
  WelcomePageState createState() => WelcomePageState();
}

class WelcomePageState extends State<WelcomePage> {
  final SocialSignIn _socialSignIn = SocialSignIn();
  final _apiUrlTextFormController = TextEditingController();
  final _socketUrlTextFormController = TextEditingController();
  final RemoteConfigStore _remoteConfigStore = Modular.get<RemoteConfigStore>();
  final UserController _userController = Modular.get<UserController>();
  final UserStorage _userStorage = Modular.get<UserStorage>();
  final UserStore _userStore = Modular.get<UserStore>();
  final RemoteConfigService _remoteConfigService =
      Modular.get<RemoteConfigService>();
  final String _logoHeroTag = '_logoHeroTag';
  late final int _remoteConfigServiceListenerId;
  bool _isStarting = true;
  bool _shouldLogoVisible = false;
  bool _showUpdateWidget = false;
  bool _isInitiated = false;
  bool _isLoadingRemoteConfig = true;

  String _userName = '';
  String _googleId = '';
  String _appleId = '';

  bool _isBottomSheetVisible = false;

  set isBottomSheetVisible(bool newVal) {
    setState(() {
      _isBottomSheetVisible = newVal;
    });
  }

  set isStarting(bool newVal) {
    setState(() {
      _isStarting = newVal;
    });
  }

  set showUpdateWidget(bool newVal) {
    setState(() {
      _showUpdateWidget = newVal;
    });
  }

  set isLoadingRemoteConfig(bool newVal) {
    setState(() {
      _isLoadingRemoteConfig = newVal;
    });
  }

  WelcomePageState();

  void _requestAppleAttributionDetails() async {
    if (mounted && !kIsWeb && Platform.isIOS) {
      // AppleSearchAdsAttribution.requestAppleAttributionDetails();
    }
  }

  set shouldLogoVisible(bool newValue) {
    setState(() {
      _shouldLogoVisible = true;
    });
  }

  @override
  initState() {
    super.initState();
    WidgetsBinding.instance
        .addPostFrameCallback((_) => _requestAppleAttributionDetails());
    setTimeout(
        duration: const Duration(seconds: 2),
        callback: () => isStarting = false);
    setTimeout(
        duration: const Duration(milliseconds: 100),
        callback: () => shouldLogoVisible = true);

    if (_remoteConfigService.isListenersExecuted) {
      _remoteConfigServiceListener();
    }
    _remoteConfigServiceListenerId =
        _remoteConfigService.addListener(_remoteConfigServiceListener);
    setTimeout(
        callback: () {
          if (_remoteConfigStore
              .state.currentAppVersionInfo.currentVersion.isNotEmpty) {
            isLoadingRemoteConfig = false;
          }
        },
        duration: const Duration(seconds: 1));
    _apiUrlTextFormController.text = AppEnv.apiUrl;
    _socketUrlTextFormController.text = AppEnv.socketUrl;
  }

  @override
  void dispose() {
    _remoteConfigService.removeListener(_remoteConfigServiceListenerId);
    super.dispose();
  }

  void _remoteConfigServiceListener() {
    try {
      final appOnStoreVersion =
          _remoteConfigStore.state.currentAppVersionInfo.currentVersion;
      final res = isGreaterThanCurrentAppVersion(appOnStoreVersion);
      if (res) {
        showUpdateWidget = true;
        return;
      }
      if (appOnStoreVersion.isNotEmpty) {
        isLoadingRemoteConfig = false;
        _verifyUserLogged();
      }
      showUpdateWidget = false;
    } catch (e) {
      print("err==>$e");
    }
  }

  _verifyUserLogged() async {
    if (_isInitiated) return;
    _isInitiated = true;
    _userController.doInitialVerification();
  }

  buttonFunc() async {
    print('=ABC=>${await _userStorage.getUserToken()}');
  }

  Widget getButtonChild({String social = 'google'}) {
    return Container(
      height: 34,
      margin: const EdgeInsets.only(left: 10),
      // width: 245,
      child: Center(
        child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              CustomImage(
                'assets/img/png/${social.toLowerCase()}_logo.png',
                width: 32,
              ),
              const Spacer(),
              Text(
                'SIGN IN WITH ${social.toUpperCase()} ACCOUNT',
                style: buttonDefaultTextStyle(CustomColors.davysGrey,
                    fontSize: 13,
                    letterSpacing: 1,
                    fontWeight: FontWeight.w500),
              ),
              const Spacer()
            ]),
      ),
    );
  }

  Widget getButton(Function() onPressed, String label,
      {Color textColor = Colors.white,
      Color color = CustomColors.button,
      Color borderColor = CustomColors.button,
      bool outlined = false,
      Widget? child}) {
    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(maxWidth: 311),
      height: 51,
      child: Button(
          onPressed: onPressed,
          outlined: outlined,
          autoSized: true,
          text: label,
          fontSize: 12,
          textColor: textColor,
          color: color,
          borderColor: borderColor,
          child: child),
    );
  }

  void handleGoogleSignIn() {
    handleSocialSignIn(tryGoogle: true);
  }

  void handleAppleSingIn() {
    handleSocialSignIn(tryGoogle: false);
  }

  Widget _buildBottomSheetButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 30),
      // constraints: const BoxConstraints(maxWidth: 340),
      child: Column(
        children: [
          getButton(handleGoogleSignIn, 'GOOGLE',
              color: Colors.white,
              textColor: CustomColors.button,
              child: getButtonChild(),
              borderColor: Colors.transparent),
          const SizedBox(height: 10),
          getButton(handleAppleSingIn, 'APPLE',
              color: Colors.white,
              textColor: CustomColors.button,
              child: getButtonChild(social: 'apple'),
              borderColor: Colors.transparent),
          const SizedBox(height: 18),
          TextButton(
              onPressed: _handleLetsGo,
              child: const Text(
                'CONTINUE WITHOUT ACCOUNT',
                style: TextStyle(
                    fontSize: 12,
                    letterSpacing: 1,
                    fontWeight: FontWeight.w700,
                    height: 0,
                    color: Colors.white,
                    decoration: TextDecoration.underline,
                    decorationThickness: 1.3,
                    decorationColor: Colors.white,
                    fontFamily: 'Roboto'),
              )),
          Container(
            // width: 300,
            child: RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  text: 'BY CONTINUING YOU ARE AGREEING WITH OUR ',
                  style: const TextStyle(
                      fontSize: 8,
                      letterSpacing: 1.2,
                      fontWeight: FontWeight.normal,
                      height: 1.4,
                      color: Colors.white,
                      fontFamily: 'Roboto'),
                  children: <TextSpan>[
                    TextSpan(
                      text: 'TERMS OF USE',
                      style: const TextStyle(
                        color: Colors.white,
                        decoration: TextDecoration.underline,
                        decorationThickness: 1.3,
                        decorationColor: Colors.white,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          // Ação quando o texto "TERMS OF USE" for clicado
                          _showTermsOfUse();
                        },
                    ),
                    const TextSpan(
                      text:
                          ' AND CONFIRM THAT YOU ARE AT LEAST 18 YEARS OLD. READ ABOUT\nOUR DATA AND COOKIE POLICY AT ',
                    ),
                    TextSpan(
                      text: 'PRIVACY POLICY',
                      style: const TextStyle(
                        color: Colors.white,
                        decoration: TextDecoration.underline,
                        decorationThickness: 1.3,
                        decorationColor: Colors.white,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          _launchPageOfPrivacyPolicy();
                        },
                    ),
                  ],
                )),
          )
        ],
      ),
    );
  }

  void _handleGoToHome() async {
    Modular.to.pushReplacementNamed(AppRoutes.home);
  }

  void _handleGoToIntro() async {
    Modular.to.pushReplacementNamed(AppRoutes.tutorial, arguments: true);
  }

  Widget _buildBottomSheetBody() {
    return Container(
        child: Column(
      children: [
        Container(
          height: 6,
          width: 40,
          margin: const EdgeInsets.only(top: 17, bottom: 22),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(30),
              color: Colors.white.withAlpha(133)),
        ),
        const Text(
          'CREATE AN ACCOUNT',
          style: TextStyle(
              fontSize: 18,
              letterSpacing: 2,
              fontWeight: FontWeight.bold,
              height: 0,
              color: Colors.white,
              fontFamily: 'Roboto'),
        ),
        const SizedBox(
          height: 22,
        ),
        _buildBottomSheetButtons(),
        const SizedBox(
          height: 30,
        ),
      ],
    ));
  }

  void _handleOpenBottomSheet() {
    isBottomSheetVisible = true;
    showModalBottomSheet<void>(
            context: context,
            isDismissible: true,
            backgroundColor: Colors.white.withAlpha(89),
            shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.vertical(top: Radius.circular(30))),
            builder: (BuildContext context) => SizedBox(
                // color: Colors.white,
                // constraints: const BoxConstraints(maxWidth: 390),
                height: 354,
                width: MediaQuery.of(context).size.width,
                child: _buildBottomSheetBody()))
        .then((value) => isBottomSheetVisible = false);
  }

  Future<SocialLoginResultEntity> tryGoogleSignIn() async {
    UserCredential? signInResult = await _socialSignIn.signInWithGoogle();
    _googleId = signInResult?.user?.uid ?? '';
    final res = await _userController.doLoginWithGoogle(_googleId);
    return SocialLoginResultEntity(
        userCredential: signInResult, loginResult: res);
  }

  Future<SocialLoginResultEntity> tryAppleSingIn() async {
    UserCredential signInResult = await _socialSignIn.signInWithApple();
    _appleId = signInResult.user?.uid ?? '';
    return SocialLoginResultEntity(
        userCredential: signInResult,
        loginResult: await _userController.doLoginWithApple(_appleId));
  }

  void handleSocialSignIn({bool tryGoogle = false}) async {
    SocialLoginResultEntity res;
    try {
      Function funcToExec = tryAppleSingIn;
      // ESocialLogin socialLogin = ESocialLogin.apple;

      if (tryGoogle) {
        // socialLogin = ESocialLogin.google;
        funcToExec = tryGoogleSignIn;
      }

      res = await funcToExec();
      // AppEvents.socialLogin(socialLogin);

      if (res.loginResult != null) {
        _userStore.setData(res.loginResult!);
        _handleGoToHome();
        return;
      }
      if (res.userCredential!.user != null) {
        _userStore.setAvatarUrl(res.userCredential?.user?.photoURL ?? '');
        _userName = (res.userCredential?.user?.displayName ?? 'NEW_PLAYER')
            .toUpperCase();
        _handleSaveUser();
      }
    } catch (e) {
      print('==>${e.toString()}');
    }
  }

  void _handleSaveUser() async {
    File? file;

    try {
      if (_userStore.state.user.avatarUrl != null) {
        if (_userStore.state.user.avatarUrl!.startsWith('http')) {
          var tempUint8List = await _userController
              .getImageFromUrl(_userStore.state.user.avatarUrl!);
          file = await uint8ListToFile(tempUint8List);
        } else {
          file = File(_userStore.state.user.avatarUrl!);
        }
      }
      if (await _userController.doRegisterUser(
          name: _userName,
          file: file,
          googleId: _googleId,
          appleId: _appleId)) {
        await Analytics.instance.logEvent(name: 'register_success');
        _handleGoToIntro();
      }
    } catch (e) {
      print('Err:URP=>$e');
      ShowMessage(message: defaultFailureMessage, type: MessageType.error);
    }
  }

  void _showTermsOfUse() {
    Modular.to.pushNamed(AppRoutes.terms);
  }

  void _launchPageOfPrivacyPolicy() async {
    Uri url = Uri.parse('https://www.quycky.app/privacy-terms');
    if (await canLaunchUrl(url)) {
      launchUrl(url);
    }
  }

  _handleLetsGo({String socialLoginType = ''}) async {
    if (socialLoginType.isNotEmpty) {
      _userController.socialRegisterLoginType = socialLoginType;
      await Analytics.instance.logEvent(name: 'start_login_social');
    } else {
      await Analytics.instance.logEvent(name: 'start_button');
    }
    Modular.to.pushReplacementNamed(AppRoutes.userRegister());

    // _userStore.setLoading(true, force: true);
    // await _userStore.getLoginResultData();
    // final userStorage = Modular.get<UserStorage>();
    // if (_userStore.state.user.id.isNotEmpty) {
    //   final result = await _userController.doInitialVerification();
    //   if (result == null) {
    //     Modular.to.pushReplacementNamed(AppRoutes.terms);
    //   } else {
    //     Modular.to.pushNamedAndRemoveUntil(AppRoutes.home, (p0) => false);
    //   }
    // } else {
    //   Modular.to.pushNamedAndRemoveUntil(AppRoutes.terms, (p0) => false);
    // }
    // _userStore.setLoading(false, force: true);

    //  Modular.to.pushReplacementNamed(AppRoutes.userRegister);
  }

  List<Widget> _buildDevelopTextForms() {
    if (!AppEnv.isDevMode) return [];
    return [
      TextFormField(
        controller: _apiUrlTextFormController,
        autofocus: true,
        onFieldSubmitted: (value) {
          AppEnv.apiUrl = value;
        },
        textInputAction: TextInputAction.done,
        keyboardType: TextInputType.name,
      ),
      TextFormField(
        controller: _socketUrlTextFormController,
        autofocus: true,
        onFieldSubmitted: (value) {
          AppEnv.socketUrl = value;
        },
        textInputAction: TextInputAction.done,
        keyboardType: TextInputType.name,
      )
    ];
  }

  Widget _buildBottomButton(void Function() onPressed, String label,
      {showLoading = false}) {
    Widget? loading;
    void Function() buttonOnPressed = onPressed;
    if (showLoading) {
      loading = const CircularProgressIndicator(color: CustomColors.orangeSoda);
      buttonOnPressed = () {};
    }
    return Center(
        child: Container(
      width: double.infinity,
      height: 51,
      constraints: const BoxConstraints(maxWidth: 312),
      child: Button(
          onPressed: buttonOnPressed,
          color: Colors.white,
          borderColor: Colors.white,
          autoSized: true,
          textColor: CustomColors.orangeSoda,
          text: label,
          child: loading),
    ));
  }

  Widget _buildTopText() {
    return const Padding(
      padding: EdgeInsets.only(top: 20.0),
      child: Text(
        'EMBRACE SEXUALITY WITH\nRESPECT, CURIOSITY, AND LOVE.',
        textAlign: TextAlign.center,
        style: TextStyle(
            fontSize: 10,
            letterSpacing: 1.8,
            fontWeight: FontWeight.bold,
            height: 2,
            color: Colors.white,
            fontFamily: 'Roboto'),
      ),
    );
  }

  Widget _buildAnimatedWidgetFadeIn(Widget child,
      {duration = const Duration(seconds: 1), isVisible = true}) {
    return AnimatedOpacity(
        opacity: isVisible ? 1.0 : 0.0, duration: duration, child: child);
  }

  bool isGreaterThanCurrentAppVersion(String appOnStoreVersion) {
    final appOnStoreVersionSplitted =
        appOnStoreVersion.split('.').map((e) => dynamicToInt(e)).toList();
    final currentAppVersionSplitted =
        AppEnv.appVersion.split('.').map((e) => dynamicToInt(e)).toList();
    final first = appOnStoreVersionSplitted[0] > currentAppVersionSplitted[0]
        ? 1
        : appOnStoreVersionSplitted[0] == currentAppVersionSplitted[0]
            ? 0
            : -1;
    final second = appOnStoreVersionSplitted[1] > currentAppVersionSplitted[1]
        ? 1
        : appOnStoreVersionSplitted[1] == currentAppVersionSplitted[1]
            ? 0
            : -1;
    if (first == 1) {
      return true;
    }
    if (second == 1) {
      return true;
    }
    if ((first == second) &&
        first == 0 &&
        appOnStoreVersionSplitted[2] > currentAppVersionSplitted[2]) {
      return true;
    }
    return false;
  }

  void _handleOpenPageToUpdate() {
    if (kIsWeb) return;

    final url = Platform.isIOS
        ? _remoteConfigStore.state.currentAppVersionInfo.appleStore
        : _remoteConfigStore.state.currentAppVersionInfo.androidStore;
    if (url.isEmpty) return;

    final uri = Uri.parse(url);
    launchUrl(uri, mode: LaunchMode.externalApplication);
  }

  Widget _buildUpdateAppWidget() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const SizedBox(
          height: 25,
        ),
        Hero(
          tag: _logoHeroTag,
          child: SizedBox(
              height: 73,
              width: 203,
              child: CustomImage(Assets.svg.quyckyWelcomeLogoWhite)),
        ),
        const Spacer(),
        const Text(
            "A NEW VERSION IS AVAILABLE.\nUPDATE NOW FOR NEW FEATURES\nAND IMPROVEMENTS!",
            textAlign: TextAlign.center,
            style: TextStyle(
                fontFamily: "Roboto",
                letterSpacing: 1.5,
                height: 1.3,
                fontWeight: FontWeight.w700,
                color: Colors.white,
                fontSize: 14)),
        const Spacer(),
        _buildBottomButton(
          _handleOpenPageToUpdate,
          "UPDATE",
        ),
        const SizedBox(
          height: 60,
        )
      ],
    );
  }

  Widget _buildMainWidget() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        _buildAnimatedWidgetFadeIn(_buildTopText(),
            isVisible: !_isStarting && !_showUpdateWidget),
        const Spacer(),
        Hero(
          tag: _logoHeroTag,
          child: _buildAnimatedWidgetFadeIn(
              SizedBox(
                  height: 85.21,
                  width: 237,
                  child: CustomImage(Assets.svg.quyckyWelcomeLogoWhite)),
              isVisible: _shouldLogoVisible,
              duration: const Duration(seconds: 1)),
        ),
        const Spacer(),
        _buildAnimatedWidgetFadeIn(
            _buildBottomButton(
              _handleOpenBottomSheet,
              "READY TO PLAY?",
              showLoading: _isLoadingRemoteConfig,
            ),
            isVisible:
                !_isBottomSheetVisible && !_isStarting && !_showUpdateWidget),
        const SizedBox(
          height: 60,
        ),
        ..._buildDevelopTextForms()
      ],
    );
  }

  Widget _buildBodyWidget() {
    return _showUpdateWidget ? _buildUpdateAppWidget() : _buildMainWidget();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: WelcomePageAnimatedBackground(
          // useDefault: true,
          // normalOpacity: 0,
          // hotOpacity: 0,
          // coldOpacity: 0,
          child: SafeArea(
        child: _buildBodyWidget(),
      )),
    );
  }
}
