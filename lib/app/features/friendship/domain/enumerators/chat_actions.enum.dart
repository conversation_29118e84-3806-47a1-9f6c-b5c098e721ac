enum EChatActions {
  // outbound
  sendMessage('sendMessage'),
  getMessages('getMessages'),
  getConversations('getConversations'),
  markAsRead('markAsRead'),
  getUnreadCount('getUnreadCount'),
  getMessagesBetweenUsers('getMessagesBetweenUsers');
  // other
  // inbound

  final String label;

  @override
  String toString() {
    return label;
  }

  const EChatActions(this.label);

  static EChatActions fromString(String label) {
    return values.firstWhere(
      (v) => v.label == label,
      orElse: () => EChatActions.getMessages,
    );
  }
}
