// lib/app/features/friendship/domain/enumerators/chat_action_enumerator.dart
class ChatActionEnumerator {
  static const String sendMessage = 'sendMessage';
  static const String getMessages = 'getMessages';
  static const String getMessagesBetweenUsers = 'getMessagesBetweenUsers';
  static const String getConversations = 'getConversations';
  static const String markAsRead = 'markAsRead';
  static const String getUnreadCount = 'getUnreadCount';

  // Ações do servidor (Outbound) - para parsing das respostas
  static const String conversationList = 'conversationList';
  static const String messagesBetweenPlayers = 'messagesBetweenPlayers';
  static const String messageList = 'messageList';
  static const String messageSent = 'messageSent';
  static const String messageReceived = 'messageReceived';
  static const String messagesUpdated = 'messagesUpdated';
  static const String conversationsUpdated = 'conversationsUpdated';
  static const String countAsRead = 'countAsRead';
  static const String unreadCountUpdated = 'unreadCountUpdated';
}
