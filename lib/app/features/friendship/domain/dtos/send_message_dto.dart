// lib/app/features/friendship/domain/dtos/send_message_dto.dart
import 'dart:convert';
import 'package:quycky/core/entities/abs_mappable.dart';

class SendMessageDTO implements AbsMappable {
  final String receiverIdentifier;
  final String content;

  SendMessageDTO({
    required this.receiverIdentifier,
    required this.content,
  });

  @override
  Map<String, dynamic> toMap() {
    return {
      'receiverIdentifier': receiverIdentifier,
      'content': content,
    };
  }

  factory SendMessageDTO.fromMap(Map<String, dynamic> map) {
    return SendMessageDTO(
      receiverIdentifier: map['receiverIdentifier'] as String,
      content: map['content'] as String,
    );
  }

  @override
  String toJson() => json.encode(toMap());

  factory SendMessageDTO.fromJson(String source) =>
      SendMessageDTO.fromMap(json.decode(source) as Map<String, dynamic>);
}
