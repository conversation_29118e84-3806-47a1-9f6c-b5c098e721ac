// lib/app/features/friendship/domain/dtos/get_unread_count_dto.dart
import 'dart:convert';
import 'package:quycky/core/entities/abs_mappable.dart';

class GetUnreadCountDTO implements AbsMappable {
  final String? conversationId; // ou otherUserIdentifier

  GetUnreadCountDTO({
    this.conversationId,
  });

  @override
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{};
    if (conversationId != null) {
      map['conversationId'] = conversationId;
    }
    return map;
  }

  factory GetUnreadCountDTO.fromMap(Map<String, dynamic> map) {
    return GetUnreadCountDTO(
      conversationId: map['conversationId'] as String?,
    );
  }

  @override
  String toJson() => json.encode(toMap());

  factory GetUnreadCountDTO.fromJson(String source) =>
      GetUnreadCountDTO.fromMap(json.decode(source) as Map<String, dynamic>);
}

// Modelo para um objeto vazio, caso seja necessário para 'getConversations' ou 'getUnreadCount'
class EmptyDataDTO implements AbsMappable {
  EmptyDataDTO();

  @override
  Map<String, dynamic> toMap() => {};

  factory EmptyDataDTO.fromMap(Map<String, dynamic> map) => EmptyDataDTO();

  @override
  String toJson() => json.encode(toMap());

  factory EmptyDataDTO.fromJson(String source) => EmptyDataDTO();
}
