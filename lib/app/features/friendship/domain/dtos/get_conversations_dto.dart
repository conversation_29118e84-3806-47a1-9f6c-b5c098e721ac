// lib/app/features/friendship/domain/dtos/get_conversations_dto.dart
import 'dart:convert';
import 'package:quycky/core/entities/abs_mappable.dart';

class GetConversationsDTO implements AbsMappable {
  final int? page;
  final int? limit;

  GetConversationsDTO({
    this.page,
    this.limit,
  });

  @override
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{};
    if (page != null) {
      map['page'] = page;
    }
    if (limit != null) {
      map['limit'] = limit;
    }
    return map;
  }

  factory GetConversationsDTO.fromMap(Map<String, dynamic> map) {
    return GetConversationsDTO(
      page: map['page'] as int?,
      limit: map['limit'] as int?,
    );
  }

  @override
  String toJson() => json.encode(toMap());

  factory GetConversationsDTO.fromJson(String source) =>
      GetConversationsDTO.fromMap(json.decode(source) as Map<String, dynamic>);
}
