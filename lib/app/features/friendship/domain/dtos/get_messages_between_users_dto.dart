// lib/app/features/friendship/domain/dtos/get_messages_between_users_dto.dart
import 'dart:convert';
import 'package:quycky/core/entities/abs_mappable.dart';

class GetMessagesBetweenUsersDTO implements AbsMappable {
  final String otherUserIdentifier;
  final int? page;
  final int? limit;

  GetMessagesBetweenUsersDTO({
    required this.otherUserIdentifier,
    this.page,
    this.limit,
  });

  @override
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{
      'otherUserIdentifier': otherUserIdentifier,
    };
    if (page != null) {
      map['page'] = page;
    }
    if (limit != null) {
      map['limit'] = limit;
    }
    return map;
  }

  factory GetMessagesBetweenUsersDTO.fromMap(Map<String, dynamic> map) {
    return GetMessagesBetweenUsersDTO(
      otherUserIdentifier: map['otherUserIdentifier'] as String,
      page: map['page'] as int?,
      limit: map['limit'] as int?,
    );
  }

  @override
  String toJson() => json.encode(toMap());

  factory GetMessagesBetweenUsersDTO.fromJson(String source) =>
      GetMessagesBetweenUsersDTO.fromMap(
          json.decode(source) as Map<String, dynamic>);
}
