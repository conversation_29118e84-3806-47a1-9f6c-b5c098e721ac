// lib/app/features/friendship/domain/dtos/chat_message_dto.dart
import 'dart:convert';
import 'package:quycky/core/entities/abs_mappable.dart';

class ChatMessageDTO<T extends AbsMappable> implements AbsMappable {
  final String action;
  final String? authorization; // Pode ser nulo se enviado no header
  final String? connectionId; // Pode ser nulo se enviado no header
  final T data;

  ChatMessageDTO({
    required this.action,
    this.authorization,
    this.connectionId,
    required this.data,
  });

  @override
  Map<String, dynamic> toMap() {
    return {
      'action': action,
      if (authorization != null) 'authorization': authorization,
      if (connectionId != null) 'connectionId': connectionId,
      'data': data.toMap(),
    };
  }

  factory ChatMessageDTO.fromMap(
      Map<String, dynamic> map, T Function(Map<String, dynamic>) dataFactory) {
    return ChatMessageDTO(
      action: map['action'] as String,
      authorization: map['authorization'] as String?,
      connectionId: map['connectionId'] as String?,
      data: dataFactory(map['data'] as Map<String, dynamic>),
    );
  }

  @override
  String toJson() => json.encode(toMap());

  factory ChatMessageDTO.fromJson(
          String source, T Function(Map<String, dynamic>) dataFactory) =>
      ChatMessageDTO.fromMap(
          json.decode(source) as Map<String, dynamic>, dataFactory);
}
