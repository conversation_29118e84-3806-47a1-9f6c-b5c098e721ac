import 'package:quycky/core/utils/type_converters.dart';

class ChatMessageEntity {
  final int id;
  final String senderIdentifier;
  final String receiverIdentifier;
  final String content;
  final DateTime createdAt;
  final bool isRead;

  ChatMessageEntity({
    required this.id,
    required this.senderIdentifier,
    required this.receiverIdentifier,
    required this.content,
    required this.createdAt,
    this.isRead = false,
  });

  factory ChatMessageEntity.fromMap(Map<String, dynamic> map) {
    return ChatMessageEntity(
      id: dynamicToInt(map['id']),
      senderIdentifier: dynamicToString(map['sender_identifier']),
      receiverIdentifier: dynamicToString(map['receiver_identifier']),
      content: dynamicToString(map['content']),
      createdAt: dynamicToDateTime(map['created_at']),
      isRead: dynamicToBoolean(map['is_read']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'sender_identifier': senderIdentifier,
      'receiver_identifier': receiverIdentifier,
      'content': content,
      'created_at': createdAt,
      'is_read': isRead,
    };
  }

  ChatMessageEntity copyWith({
    int? id,
    String? senderIdentifier,
    String? receiverIdentifier,
    String? content,
    DateTime? createdAt,
    bool? isRead,
  }) {
    return ChatMessageEntity(
      id: id ?? this.id,
      senderIdentifier: senderIdentifier ?? this.senderIdentifier,
      receiverIdentifier: receiverIdentifier ?? this.receiverIdentifier,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      isRead: isRead ?? this.isRead,
    );
  }
}
