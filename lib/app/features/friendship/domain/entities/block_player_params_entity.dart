class BlockPlayerParamsEntity {
  final int currentUserId; // ID do usuário que está realizando o bloqueio
  final int userIdToBlock; // ID do usuário que será bloqueado

  BlockPlayerParamsEntity({
    required this.currentUserId,
    required this.userIdToBlock,
  });

  Map<String, dynamic> toJson() {
    return {
      'user_id': currentUserId,
      'blocked_user_id': userIdToBlock,
    };
  }

  // Se precisar de fromJson (menos comum para params, mais para responses):
  // factory BlockPlayerParamsEntity.fromJson(Map<String, dynamic> json) {
  //   return BlockPlayerParamsEntity(
  //     currentUserId: json['user_id'] as int,
  //     userIdToBlock: json['blocked_user_id'] as int,
  //   );
  // }
}

class UnblockPlayerParamsEntity {
  final int currentUserId; // ID do usuário que está realizando o desbloqueio
  final int userIdToUnblock; // ID do usuário que será desbloqueado

  UnblockPlayerParamsEntity({
    required this.currentUserId,
    required this.userIdToUnblock,
  });

  Map<String, dynamic> toJson() {
    return {
      'user_id': currentUserId,
      'blocked_user_id': userIdToUnblock,
    };
  }
}
