import 'package:equatable/equatable.dart';
import 'package:quycky/core/utils/type_converters.dart';

class BlockedUserDetailsEntity extends Equatable {
  final int id;
  final String uuid;
  final String? appleId;
  final String? googleId;
  final String name;
  final String? email;
  final String? phoneNumber;
  final String identifier;
  final String? onesignalId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime lastaccessAt;
  final String? avatar;
  final String? avatarUrl;
  final int pontuation;

  const BlockedUserDetailsEntity({
    required this.id,
    required this.uuid,
    this.appleId,
    this.googleId,
    required this.name,
    this.email,
    this.phoneNumber,
    required this.identifier,
    this.onesignalId,
    required this.createdAt,
    required this.updatedAt,
    required this.lastaccessAt,
    this.avatar,
    this.avatarUrl,
    this.pontuation = 0,
  });

  factory BlockedUserDetailsEntity.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      final now = DateTime.now();
      return BlockedUserDetailsEntity(
          id: 0,
          uuid: '',
          name: '',
          identifier: '',
          createdAt: now,
          updatedAt: now,
          lastaccessAt: now);
    }
    return BlockedUserDetailsEntity(
      id: dynamicToInt(json['id']),
      uuid: dynamicToString(json['uuid']),
      appleId: dynamicToString(json['apple_id']),
      googleId: dynamicToString(json['google_id']),
      name: dynamicToString(json['name']),
      email: dynamicToString(json['email']),
      phoneNumber: dynamicToString(json['phone_number']),
      identifier: dynamicToString(json['identifier']),
      onesignalId: dynamicToString(json['onesignal_id']),
      createdAt: dynamicToDateTime(json['created_at']),
      updatedAt: dynamicToDateTime(json['updated_at']),
      lastaccessAt: dynamicToDateTime(json['lastaccess_at']),
      avatar: dynamicToString(json['avatar']),
      avatarUrl: dynamicToString(json['avatarUrl']),
      pontuation: dynamicToInt(json['pontuation']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'uuid': uuid,
      'apple_id': appleId,
      'google_id': googleId,
      'name': name,
      'email': email,
      'phone_number': phoneNumber,
      'identifier': identifier,
      'onesignal_id': onesignalId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'lastaccess_at': lastaccessAt.toIso8601String(),
      'avatar': avatar,
      'avatarUrl': avatarUrl,
    };
  }

  @override
  List<Object?> get props => [
        id,
        uuid,
        appleId,
        googleId,
        name,
        email,
        phoneNumber,
        identifier,
        onesignalId,
        createdAt,
        updatedAt,
        lastaccessAt,
        avatar,
        avatarUrl,
      ];
}
