import 'package:flutter/material.dart';
import 'package:quycky/app/features/friendship/domain/entities/friendship_list_item_entity.dart';
import 'package:quycky/app/features/user/domain/entities/user_entity.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/avatar.dart';
import 'package:quycky/core/utils/image_assets.dart';
import 'package:quycky/core/utils/quycky_icons_icons.dart';
import 'package:sizer/sizer.dart';

class FriendshipItem extends StatefulWidget {
  final FriendshipListItem friend;
  final bool isMatchButtonEnabled;
  final int qtyNewMessages;
  final void Function(int id) onOpenFriendProfile;
  final void Function(String id) onOpenFriendMatch;
  final void Function(UserEntity friend) onOpenFriendChat;

  const FriendshipItem({
    super.key,
    required this.friend,
    this.qtyNewMessages = 0,
    required this.onOpenFriendProfile,
    required this.onOpenFriendMatch,
    required this.onOpenFriendChat,
    this.isMatchButtonEnabled = false,
  });

  @override
  State<FriendshipItem> createState() => _FriendshipItem();
}

class _FriendshipItem extends State<FriendshipItem> {
  void handleHeartSearchClick() {
    if (widget.isMatchButtonEnabled) {
      widget.onOpenFriendMatch(widget.friend.user.id);
    }
  }

  Widget getBadgeQtyNewMessages() {
    if (widget.qtyNewMessages == 0) {
      return Container();
    }
    String label =
        widget.qtyNewMessages > 9 ? '9+' : widget.qtyNewMessages.toString();
    return Container(
        width: 5.38.w,
        height: 2.96.h,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: CustomColors.redPigment,
            shape: BoxShape.rectangle),
        child: Center(
          child: Text(
            label,
            style: const TextStyle(
                fontFamily: "SFProText",
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: 14),
          ),
        ));
  }

  Widget getIconButton() {
    Color iconColor =
        widget.isMatchButtonEnabled ? Colors.white : CustomColors.mauvelous;
    return GestureDetector(
      onTap: handleHeartSearchClick,
      child: Container(
        height: double.infinity,
        padding: EdgeInsets.only(right: 5.13.w),
        color: Colors.transparent,
        child: Container(
          width: 12.3.w,
          height: 5.68.h,
          decoration: BoxDecoration(
            color: CustomColors.primary,
            shape: BoxShape.circle,
          ),
          child: Icon(
            QuyckyIcons.heart_search,
            color: iconColor,
            size: 2.62.h,
          ),
        ),
      ),
    );
    // return Container(
    //     child: Icon(
    //   QuyckyIcons.heart_search,
    //   color: Colors.white,
    //   size: 2.62.h,
    // ));
  }

  Widget getWidget() {
    return Container(
      height: 10.66.h,
      width: 87.18.w,
      decoration: const BoxDecoration(
        color: CustomColors.orangeSoda30,
        borderRadius: BorderRadius.all(Radius.circular(25)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GestureDetector(
            onTap: () => widget.onOpenFriendChat(widget.friend.user),
            // widget.onOpenFriendProfile(int.parse(widget.friend.user.id)),
            child: Container(
              width: widget.qtyNewMessages == 0 ? 64.w : 57.w,
              color: Colors.transparent,
              child: Row(
                children: [
                  Padding(
                    padding: EdgeInsets.only(left: 3.84.w, right: 4.1.w),
                    child: Avatar(
                        imagePath: widget.friend.user.avatarUrl,
                        size: 6.87.h,
                        addPhotoButton: false),
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        width: 40.w,
                        child: Text(
                          widget.friend.user.name,
                          style: const TextStyle(
                              overflow: TextOverflow.ellipsis,
                              fontFamily: "SFProText",
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              fontSize: 14),
                        ),
                      ),
                      SizedBox(
                        height: 1.89.h,
                      ),
                      Row(
                        children: [
                          Padding(
                            padding: EdgeInsets.only(right: 1.28.w),
                            child: Image.asset(
                              ImageAssets.fire,
                              height: 1.65.h,
                              width: 3.33.w,
                            ),
                          ),
                          Text(
                            widget.friend.user.pontuation.toString(),
                            style: const TextStyle(
                                fontFamily: "SFProText",
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                fontSize: 10),
                          ),
                        ],
                      )
                    ],
                  ),
                ],
              ),
            ),
          ),
          getBadgeQtyNewMessages(),
          getIconButton(),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return getWidget();
  }
}
