import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:quycky/app/features/friendship/domain/entities/conversation_entity.dart';
import 'package:quycky/app/features/friendship/presenter/store/chat_store.dart';
import 'package:quycky/app/features/friendship/presenter/widgets/chat_unread_badge.dart';
import 'package:quycky/app/widgets/avatar.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:sizer/sizer.dart';

/// Widget que representa um item de conversa na lista de chats
class ChatConversationItem extends StatelessWidget {
  final ConversationEntity conversation;
  final VoidCallback onTap;

  const ChatConversationItem({
    Key? key,
    required this.conversation,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final chatStore = Modular.get<ChatStore>();
    
    // Obtém a contagem de mensagens não lidas do ChatStore
    final unreadCount = chatStore.getUnreadCountForUser(conversation.userIdentifier);
    
    return InkWell(
      onTap: () {
        // Zera a contagem de mensagens não lidas ao abrir a conversa
        chatStore.clearUnreadCount(conversation.userIdentifier);
        onTap();
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Colors.grey.withOpacity(0.2),
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          children: [
            // Avatar com badge de mensagens não lidas
            ChatUnreadBadge(
              userIdentifier: conversation.userIdentifier,
              child: Avatar(
                imagePath: conversation.userAvatar,
                size: 6.h,
                addPhotoButton: false,
              ),
            ),
            SizedBox(width: 3.w),
            
            // Informações da conversa
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Nome do usuário
                  Text(
                    conversation.userName,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: unreadCount > 0 ? FontWeight.bold : FontWeight.w500,
                      color: Colors.white,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 0.5.h),
                  
                  // Última mensagem
                  Text(
                    conversation.lastMessage.content,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: unreadCount > 0 ? FontWeight.w500 : FontWeight.w400,
                      color: unreadCount > 0 
                          ? Colors.white.withOpacity(0.9)
                          : Colors.white.withOpacity(0.7),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            
            // Coluna com horário e contador
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // Horário da última mensagem
                Text(
                  _formatTime(conversation.lastMessage.createdAt),
                  style: TextStyle(
                    fontSize: 12,
                    color: unreadCount > 0 
                        ? CustomColors.primary
                        : Colors.white.withOpacity(0.6),
                    fontWeight: unreadCount > 0 ? FontWeight.w600 : FontWeight.w400,
                  ),
                ),
                
                if (unreadCount > 0) ...[
                  SizedBox(height: 0.5.h),
                  // Badge com número de mensagens não lidas
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: CustomColors.primary,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 20,
                      minHeight: 20,
                    ),
                    child: Text(
                      unreadCount > 99 ? '99+' : unreadCount.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 11,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now';
    }
  }
}
