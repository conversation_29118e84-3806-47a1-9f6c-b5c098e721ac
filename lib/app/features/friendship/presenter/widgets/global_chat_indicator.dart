import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_triple/flutter_triple.dart';
import 'package:quycky/app/features/friendship/presenter/store/chat_store.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:sizer/sizer.dart';

/// Widget global que pode ser usado em qualquer lugar da aplicação
/// para mostrar um indicador de mensagens não lidas
class GlobalChatIndicator extends StatelessWidget {
  final VoidCallback? onTap;
  final bool showAsFloatingButton;
  final bool showAsAppBarAction;

  const GlobalChatIndicator({
    Key? key,
    this.onTap,
    this.showAsFloatingButton = false,
    this.showAsAppBarAction = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final chatStore = Modular.get<ChatStore>();

    return ScopedBuilder<ChatStore, dynamic>(
      store: chatStore,
      onState: (context, state) {
        final totalUnread = chatStore.totalUnreadCount;
        final hasNewMessages = state.hasNewMessages;

        if (showAsFloatingButton) {
          return _buildFloatingButton(totalUnread, hasNewMessages);
        }

        if (showAsAppBarAction) {
          return _buildAppBarAction(totalUnread, hasNewMessages);
        }

        return _buildDefaultIndicator(totalUnread, hasNewMessages);
      },
    );
  }

  Widget _buildFloatingButton(int totalUnread, bool hasNewMessages) {
    return FloatingActionButton(
      onPressed: onTap,
      backgroundColor: CustomColors.primary,
      child: Stack(
        children: [
          const Icon(
            Icons.chat_bubble_outline,
            color: Colors.white,
            size: 28,
          ),
          if (hasNewMessages)
            Positioned(
              right: 0,
              top: 0,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: CustomColors.error,
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: Colors.white, width: 2),
                ),
                constraints: const BoxConstraints(
                  minWidth: 20,
                  minHeight: 20,
                ),
                child: Text(
                  totalUnread > 99 ? '99+' : totalUnread.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAppBarAction(int totalUnread, bool hasNewMessages) {
    return IconButton(
      onPressed: onTap,
      icon: Stack(
        children: [
          const Icon(
            Icons.chat_bubble_outline,
            color: Colors.white,
            size: 24,
          ),
          if (hasNewMessages)
            Positioned(
              right: 0,
              top: 0,
              child: Container(
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: CustomColors.error,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.white, width: 1),
                ),
                constraints: const BoxConstraints(
                  minWidth: 16,
                  minHeight: 16,
                ),
                child: Text(
                  totalUnread > 9 ? '9+' : totalUnread.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDefaultIndicator(int totalUnread, bool hasNewMessages) {
    if (!hasNewMessages) {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 2.w, vertical: 1.h),
        padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
        decoration: BoxDecoration(
          color: CustomColors.primary,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.chat_bubble,
              color: Colors.white,
              size: 16,
            ),
            SizedBox(width: 1.w),
            Text(
              totalUnread > 99 ? '99+ new messages' : '$totalUnread new message${totalUnread > 1 ? 's' : ''}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Widget específico para mostrar resumo de mensagens em uma lista
class ChatSummaryListTile extends StatelessWidget {
  final VoidCallback? onTap;

  const ChatSummaryListTile({
    Key? key,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final chatStore = Modular.get<ChatStore>();

    return ScopedBuilder<ChatStore, dynamic>(
      store: chatStore,
      onState: (context, state) {
        final totalUnread = chatStore.totalUnreadCount;
        final hasNewMessages = state.hasNewMessages;

        if (!hasNewMessages) {
          return const SizedBox.shrink();
        }

        return ListTile(
          leading: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: CustomColors.primary.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.chat_bubble,
              color: CustomColors.primary,
              size: 24,
            ),
          ),
          title: const Text(
            'New Messages',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          subtitle: Text(
            totalUnread > 99 
                ? 'You have 99+ unread messages'
                : 'You have $totalUnread unread message${totalUnread > 1 ? 's' : ''}',
            style: TextStyle(
              color: Colors.white.withOpacity(0.8),
            ),
          ),
          trailing: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: CustomColors.error,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              totalUnread > 99 ? '99+' : totalUnread.toString(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          onTap: onTap,
        );
      },
    );
  }
}

/// Widget para mostrar estatísticas detalhadas do chat
class ChatStatsWidget extends StatelessWidget {
  const ChatStatsWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final chatStore = Modular.get<ChatStore>();

    return ScopedBuilder<ChatStore, dynamic>(
      store: chatStore,
      onState: (context, state) {
        final totalUnread = chatStore.totalUnreadCount;
        final totalConversations = state.unreadMessagesCount.length;
        final activeConversations = state.unreadMessagesCount.values
            .where((count) => count > 0)
            .length;

        return Container(
          padding: EdgeInsets.all(4.w),
          margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Chat Statistics',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 2.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('Total Unread', totalUnread.toString()),
                  _buildStatItem('Active Chats', activeConversations.toString()),
                  _buildStatItem('Total Chats', totalConversations.toString()),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 0.5.h),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withOpacity(0.7),
            fontSize: 12,
          ),
        ),
      ],
    );
  }
}
