import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_triple/flutter_triple.dart';
import 'package:quycky/app/features/friendship/presenter/store/chat_store.dart';
import 'package:quycky/app/theme/colors.dart';

/// Widget que exibe um badge com a contagem de mensagens não lidas
class ChatUnreadBadge extends StatelessWidget {
  final String? userIdentifier;
  final Widget child;
  final bool showTotal;

  const ChatUnreadBadge({
    Key? key,
    this.userIdentifier,
    required this.child,
    this.showTotal = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final chatStore = Modular.get<ChatStore>();

    return ScopedBuilder<ChatStore, dynamic>(
      store: chatStore,
      onState: (context, state) {
        int unreadCount;
        
        if (showTotal) {
          // Mostra o total de mensagens não lidas
          unreadCount = chatStore.totalUnreadCount;
        } else if (userIdentifier != null) {
          // Mostra mensagens não lidas para um usuário específico
          unreadCount = chatStore.getUnreadCountForUser(userIdentifier!);
        } else {
          unreadCount = 0;
        }

        return Stack(
          children: [
            child,
            if (unreadCount > 0)
              Positioned(
                right: 0,
                top: 0,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: CustomColors.error,
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: Colors.white, width: 1),
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 20,
                    minHeight: 20,
                  ),
                  child: Text(
                    unreadCount > 99 ? '99+' : unreadCount.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}

/// Widget que exibe apenas um indicador de novas mensagens (ponto vermelho)
class ChatNewMessageIndicator extends StatelessWidget {
  final String? userIdentifier;
  final Widget child;
  final bool showForTotal;

  const ChatNewMessageIndicator({
    Key? key,
    this.userIdentifier,
    required this.child,
    this.showForTotal = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final chatStore = Modular.get<ChatStore>();

    return ScopedBuilder<ChatStore, dynamic>(
      store: chatStore,
      onState: (context, state) {
        bool hasNewMessages;
        
        if (showForTotal) {
          // Verifica se há mensagens não lidas no total
          hasNewMessages = chatStore.state.hasNewMessages;
        } else if (userIdentifier != null) {
          // Verifica se há mensagens não lidas para um usuário específico
          hasNewMessages = chatStore.getUnreadCountForUser(userIdentifier!) > 0;
        } else {
          hasNewMessages = false;
        }

        return Stack(
          children: [
            child,
            if (hasNewMessages)
              Positioned(
                right: 2,
                top: 2,
                child: Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: CustomColors.error,
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: Colors.white, width: 1),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}
