# ChatStore - Gerenciamento de Estado do Chat

O `ChatStore` é responsável por gerenciar o estado global das mensagens de chat na aplicação, incluindo contagens de mensagens não lidas e lista de mensagens atuais.

## Funcionalidades

### 1. Contagem de Mensagens Não Lidas
- **Map<String, int> unreadMessagesCount**: Armazena a quantidade de mensagens não lidas por usuário (identificador do usuário como chave)
- **bool hasNewMessages**: Indica se há mensagens novas no total

### 2. Lista de Mensagens Atuais
- **List<ChatMessageEntity> currentMessages**: Lista das mensagens da conversa atual (sempre atualizada quando `getMessagesBetweenUsers` é executado)

## Métodos Principais

### Gerenciamento de Contagem de Mensagens Não Lidas

```dart
// Atualiza ou adiciona a contagem para um usuário específico
chatStore.updateUnreadCount(String userIdentifier, int count)

// Incrementa a contagem para um usuário específico
chatStore.incrementUnreadCount(String userIdentifier)

// Zera a contagem para um usuário específico
chatStore.clearUnreadCount(String userIdentifier)

// Obtém a contagem para um usuário específico
int count = chatStore.getUnreadCountForUser(String userIdentifier)

// Obtém a contagem total de mensagens não lidas
int total = chatStore.totalUnreadCount
```

### Gerenciamento de Mensagens

```dart
// Atualiza a lista completa de mensagens (usado quando getMessagesBetweenUsers é executado)
chatStore.updateCurrentMessages(List<ChatMessageEntity> messages)

// Adiciona uma nova mensagem à lista atual
chatStore.addMessage(ChatMessageEntity message)

// Limpa todos os dados do store
chatStore.clear()
```

## Como Usar

### 1. Injeção de Dependência
O ChatStore já está registrado no `app_module.dart`:

```dart
Bind.lazySingleton((i) => ChatStore()),
```

### 2. Uso em Widgets

```dart
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final chatStore = Modular.get<ChatStore>();
    
    return ScopedBuilder<ChatStore, dynamic>(
      store: chatStore,
      onState: (context, state) {
        final totalUnread = chatStore.totalUnreadCount;
        final hasNewMessages = state.hasNewMessages;
        
        return Text('Mensagens não lidas: $totalUnread');
      },
    );
  }
}
```

### 3. Integração com Socket

No `friend_chat.dart`, o ChatStore é integrado com os eventos do socket:

```dart
// Quando uma nova mensagem é recebida
case ChatActionEnumerator.messageReceived:
  final newMessage = ChatMessageEntity.fromMap(data);
  _chatStore.addMessage(newMessage);
  if (newMessage.receiverIdentifier == _userStore.state.user.id) {
    _chatStore.incrementUnreadCount(newMessage.senderIdentifier);
  }

// Quando mensagens são carregadas
case ChatActionEnumerator.messagesBetweenPlayers:
  final messages = /* parse messages */;
  _chatStore.updateCurrentMessages(messages);
  _chatStore.clearUnreadCount(widget.friend.identifier);

// Quando contagens são atualizadas
case ChatActionEnumerator.unreadCountUpdated:
  final userUnreadCounts = data['userUnreadCounts'];
  userUnreadCounts.forEach((userIdentifier, count) {
    _chatStore.updateUnreadCount(userIdentifier, count);
  });
```

## Widgets Auxiliares

### ChatUnreadBadge
Exibe um badge com a contagem de mensagens não lidas:

```dart
ChatUnreadBadge(
  userIdentifier: 'user123',
  child: Avatar(imagePath: userAvatar),
)
```

### ChatNewMessageIndicator
Exibe apenas um indicador visual (ponto vermelho):

```dart
ChatNewMessageIndicator(
  userIdentifier: 'user123',
  child: Icon(Icons.chat),
)
```

### ChatSummaryWidget
Exibe um resumo das mensagens não lidas:

```dart
ChatSummaryWidget(
  onTap: () => Navigator.push(/* ir para lista de chats */),
)
```

### ChatIconWithBadge
Ícone compacto com badge:

```dart
ChatIconWithBadge(
  onTap: () => Navigator.push(/* ir para chats */),
  size: 24,
)
```

## Exemplo de Uso Completo

```dart
class ChatListPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final chatStore = Modular.get<ChatStore>();
    
    return Scaffold(
      appBar: AppBar(
        title: Text('Chats'),
        actions: [
          ChatIconWithBadge(
            onTap: () => _openChatSettings(),
          ),
        ],
      ),
      body: Column(
        children: [
          // Resumo de mensagens não lidas
          ChatSummaryWidget(
            onTap: () => _scrollToFirstUnread(),
          ),
          
          // Lista de conversas
          Expanded(
            child: ListView.builder(
              itemCount: conversations.length,
              itemBuilder: (context, index) {
                final conversation = conversations[index];
                return ChatConversationItem(
                  conversation: conversation,
                  onTap: () => _openChat(conversation),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
```

## Observações Importantes

1. **Persistência**: O ChatStore mantém o estado apenas em memória. Para persistir dados, implemente um storage adicional.

2. **Sincronização**: O store é atualizado automaticamente através dos eventos do socket no `friend_chat.dart`.

3. **Performance**: Use `ScopedBuilder` para reagir apenas às mudanças necessárias e evitar rebuilds desnecessários.

4. **Limpeza**: Chame `chatStore.clear()` quando necessário (ex: logout do usuário).
