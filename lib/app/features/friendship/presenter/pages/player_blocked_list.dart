import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:quycky/app/features/friendship/domain/entities/blocked_player_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/blocked_user_details_entity.dart';
import 'package:quycky/app/features/friendship/presenter/controllers/friendship_controller.dart';
import 'package:quycky/app/widgets/app_header.dart';
import 'package:quycky/app/widgets/avatar.dart';
import 'package:quycky/app/widgets/button.dart';
import 'package:quycky/app/widgets/gradient_container.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/core/utils/image_assets.dart';
import 'package:quycky/core/utils/quycky_icons_icons.dart';
import 'package:quycky/core/utils/show_message.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:sizer/sizer.dart';

class PlayerBlockedList extends StatefulWidget {
  const PlayerBlockedList({super.key});

  @override
  State<PlayerBlockedList> createState() => _PlayerBlockedListState();
}

class _PlayerBlockedListState extends State<PlayerBlockedList> {
  final _controller = Modular.get<FriendshipController>();
  bool _isLoading = false;
  final List<BlockedPlayerEntity> _blockedPlayersList = [];

  bool get isLoading => _isLoading;
  set isLoading(bool value) {
    setState(() {
      _isLoading = value;
    });
  }

  @override
  void initState() {
    _getBlockedPlayersList();
    super.initState();
  }

  void _getBlockedPlayersList() async {
    isLoading = true;
    final res = await _controller.getBlockedPlayersList();

    _blockedPlayersList.clear();
    _blockedPlayersList.addAll(res);

    // Para teste: se não há dados reais, adiciona dados de exemplo
    // Remova este bloco quando a API estiver funcionando corretamente
    // if (_blockedPlayersList.isEmpty) {
    //   _addTestData();
    // }

    isLoading = false;
  }

  // Método para adicionar dados de teste (remover em produção)
  void _addTestData() {
    final now = DateTime.now();
    _blockedPlayersList.addAll([
      BlockedPlayerEntity(
        id: 1,
        userId: 'current_user_id',
        blockedUserId: 'blocked_user_1',
        createdAt: now,
        updatedAt: now,
        blockedUser: BlockedUserDetailsEntity(
          id: 1,
          uuid: 'uuid_1',
          name: 'Alex Thunder',
          identifier: '12345',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          lastaccessAt: DateTime.now(),
          avatarUrl: 'https://i.pravatar.cc/150?img=1',
        ),
      ),
      BlockedPlayerEntity(
        id: 2,
        userId: 'current_user_id',
        blockedUserId: 'blocked_user_2',
        createdAt: now,
        updatedAt: now,
        blockedUser: BlockedUserDetailsEntity(
          id: 22,
          uuid: 'uuid_2',
          name: 'Sarah Storm',
          identifier: '67890',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          lastaccessAt: DateTime.now(),
          pontuation: 33,
          avatarUrl: 'https://i.pravatar.cc/150?img=2',
        ),
      ),
      BlockedPlayerEntity(
        id: 3,
        userId: 'current_user_id',
        blockedUserId: 'blocked_user_3',
        createdAt: now,
        updatedAt: now,
        blockedUser: BlockedUserDetailsEntity(
          id: 3,
          uuid: 'uuid_3',
          name: 'Mike Lightning',
          identifier: '54321',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          lastaccessAt: DateTime.now(),
          avatarUrl: 'https://i.pravatar.cc/150?img=3',
        ),
      ),
    ]);
  }

  @override
  void dispose() {
    super.dispose();
  }

  void handleBack() {
    Modular.to.pop();
  }

  PreferredSize _buildAppHeader() {
    return PreferredSize(
      preferredSize: Size.fromHeight(11.5.h),
      child: SafeArea(
        child: AppHeader(
          title: 'BLOCKED PLAYERS',
          logoSectionLeftWidget: IconButton(
              onPressed: handleBack,
              icon: Icon(
                QuyckyIcons.arrow_left_circle,
                color: Colors.white,
                size: 23,
              )),
        ),
      ),
    );
  }

  Widget _buildNoDataWidget() {
    return Center(
      child: Text(
        'Your blocked list is currently empty.',
        style: TextStyle(
          color: Colors.white,
          fontSize: 14,
          height: 1.4,
          letterSpacing: 0.25,
          fontFamily: 'Roboto',
        ),
      ),
    );
  }

  Widget _buildSkeletonWidget() {
    return Container(
      padding: EdgeInsets.only(top: 3.64.h, right: 3.7.w, left: 3.7.w),
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        itemCount: 5,
        itemBuilder: (BuildContext context, int index) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 14.0),
            child: _buildSkeletonItem(),
          );
        },
      ),
    );
  }

  Widget _buildSkeletonItem() {
    return Skeletonizer(
      enabled: true,
      child: Container(
        height: 10.67.h,
        width: 87.18.w,
        decoration: const BoxDecoration(
          color: CustomColors.orangeSoda30,
          borderRadius: BorderRadius.all(Radius.circular(25)),
        ),
        child: Row(
          children: [
            Padding(
              padding: EdgeInsets.only(left: 3.84.w, right: 4.1.w),
              child: Container(
                height: 6.87.h,
                width: 6.87.h,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(50),
                ),
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    height: 14,
                    width: 100,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  SizedBox(height: 4),
                  Container(
                    height: 12,
                    width: 60,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.only(right: 3.84.w),
              child: Container(
                height: 4.h,
                width: 20.w,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBlockedPlayersList() {
    return Container(
      padding: EdgeInsets.only(top: 3.64.h, right: 3.7.w, left: 3.7.w),
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        itemCount: _blockedPlayersList.length,
        itemBuilder: (BuildContext context, int index) {
          final blockedPlayer = _blockedPlayersList[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 14.0),
            child: _buildBlockedPlayerItem(blockedPlayer),
          );
        },
      ),
    );
  }

  Widget _buildBlockedPlayerItem(BlockedPlayerEntity blockedPlayer) {
    final blockedUser = blockedPlayer.blockedUser;

    return Container(
      height: 10.67.h,
      // width: 87.18.w, // Removido para permitir que o ListView gerencie a largura
      decoration: const BoxDecoration(
        color: CustomColors.orangeSoda30,
        borderRadius: BorderRadius.all(Radius.circular(25)),
      ),
      child: Row(
        children: [
          Padding(
            padding: EdgeInsets.only(left: 3.84.w, right: 4.1.w),
            child: Avatar(
              imagePath: blockedUser.avatarUrl,
              size: 6.87.h,
              addPhotoButton: false,
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  blockedUser.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Roboto',
                    letterSpacing: 0.1,
                  ),
                ),
                SizedBox(
                  height: 1.89.h,
                ),
                Row(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(right: 1.28.w),
                      child: Image.asset(
                        ImageAssets.fire,
                        height: 1.65.h,
                        width: 3.33.w,
                      ),
                    ),
                    Text(
                      blockedUser.pontuation.toString(),
                      style: const TextStyle(
                          fontFamily: "Roboto",
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          fontSize: 10),
                    ),
                  ],
                )
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(right: 3.84.w),
            child: SizedBox(
              height: 4.38.h,
              // Removido width para permitir que o Button defina sua própria largura
              // ou para que o Row o restrinja adequadamente.
              child: Button(
                onPressed: () => _handleUnblockPlayer(blockedPlayer.id),
                text: 'UNBLOCK',
                fontSize: 12,
                // autoSized: true, // Removido para evitar problemas de constraint
                // Adicionar um padding interno ao botão se necessário para o tamanho
                padding: EdgeInsets.symmetric(
                    horizontal: 12.0), // Exemplo de padding
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleUnblockPlayer(int blockId) async {
    try {
      final success = await _controller.unblockUser(blockId);
      if (success) {
        // setState(() {
        //   _blockedPlayersList
        //       .removeWhere((player) => player.blockedUser.id == blockId);
        // });
        _getBlockedPlayersList();
        // Optionally show success message
        // if (mounted) {
        //   ScaffoldMessenger.of(context).showSnackBar(
        //     const SnackBar(content: Text('Player unblocked successfully')),
        //   );
        // }
      }
      // else {
      //   // Show error message
      //   if (mounted) {
      //     ScaffoldMessenger.of(context).showSnackBar(
      //       const SnackBar(content: Text('Failed to unblock player')),
      //     );
      //   }
      // }
    } catch (e) {
      // Show error message
      if (mounted) {
        ShowMessage(
            message: 'An error occurred while unblocking player',
            type: MessageType.error);
      }
    }
  }

  Widget _buildBody() {
    return isLoading
        ? _buildSkeletonWidget()
        : _blockedPlayersList.isEmpty
            ? _buildNoDataWidget()
            : _buildBlockedPlayersList();
  }

  @override
  Widget build(BuildContext context) {
    return GradientContainer(
      useDefault: true,
      coldOpacity: 0,
      normalOpacity: 0,
      hotOpacity: 0,
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: _buildAppHeader(),
        body: _buildBody(),
      ),
    );
  }
}
