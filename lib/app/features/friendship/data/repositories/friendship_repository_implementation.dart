import 'package:dartz/dartz.dart';
import 'package:quycky/app/features/friendship/data/datasources/friendship_datasource.dart';
import 'package:quycky/app/features/friendship/domain/entities/blocked_player_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/block_player_params_entity.dart'; // Adicionado
import 'package:quycky/app/features/friendship/domain/entities/friend_match_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/friendship_profile_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/friendship_invite_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/friendship_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/invitation_ticket_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/profile_temp_code_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/report_player_params_entity.dart'; // Adicionado
import 'package:quycky/app/features/friendship/domain/repositories/friendship_repository.dart';
import 'package:quycky/app/features/friendship/domain/request_entities/invite_request.dart';
import 'package:quycky/core/usecase/errors/failure.dart';
import 'package:quycky/core/usecase/errors/exceptions.dart';

class FriendshipRepositoryImplementation implements IFriendshipRepository {
  final IFriendshipDatasource datasource;

  FriendshipRepositoryImplementation(this.datasource);

  @override
  Future<Either<Failure, FriendshipInviteEntity>> acceptInvite(int id) async {
    try {
      final result = await datasource.acceptInvite(id);
      return Right(result);
    } on Exception {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, FriendProfileEntity>> getFriendProfile(int id) async {
    try {
      final result = await datasource.getFriendProfile(id);
      return Right(result);
    } on Exception {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, List<FriendshipInviteEntity>>>
      getFriendshipInvitations(int id) async {
    try {
      final result = await datasource.getFriendshipInvitations(id);
      return Right(result);
    } on Exception {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, List<FriendshipInviteEntity>>>
      getFriendshipInvitationsReceived(int id) async {
    try {
      final result = await datasource.getFriendshipInvitationsReceived(id);
      return Right(result);
    } on Exception {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, List<FriendshipEntity>>> getFriendships(int id) async {
    try {
      final result = await datasource.getFriendships(id);
      return Right(result);
    } on Exception {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, FriendshipInviteEntity>> inviteUser(
      InviteRequest data) async {
    try {
      final result = await datasource.inviteUser(data);
      return Right(result);
    } on Exception {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, FriendshipInviteEntity>> orderInvitation(
      InviteRequest data) async {
    try {
      final result = await datasource.orderInvitation(data);
      return Right(result);
    } on Exception {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, FriendshipInviteEntity>> rejectInvite(int id) async {
    try {
      final result = await datasource.rejectInvite(id);
      return Right(result);
    } on Exception {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, FriendMatchEntity>> checkFriendMatch(
      String id, bool isNewFriend) async {
    try {
      final result = await datasource.checkFriendMatch(id, isNewFriend);
      return Right(result as FriendMatchEntity);
    } on Exception {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, List<FriendMatchEntity>>>
      getFriendMatchHistory() async {
    try {
      final result = await datasource.getFriendMatchHistory();
      result.sort((a, b) => b.matchPercent.compareTo(a.matchPercent));
      return Right(result as List<FriendMatchEntity>);
    } on Exception {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, bool>> removeFriend(int friendId) async {
    try {
      final result = await datasource.removeFriend(friendId);
      return Right(result);
    } on Exception {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, ProfileTempCodeEntity>>
      generateProfileTempCode() async {
    try {
      final result = await datasource.generateProfileTempCode();
      if (result['data'] != null) {
        final profileTempCode = ProfileTempCodeEntity.fromJson(result['data']);
        return Right(profileTempCode);
      }
      return const Left(ServerFailure());
    } on Exception catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, ProfileTempCodeEntity>> validateProfileTempCode(
      String code) async {
    try {
      final result = await datasource.validateProfileTempCode(code);
      if (result['data'] != null) {
        final profileTempCode = ProfileTempCodeEntity.fromJson(result['data']);
        return Right(profileTempCode);
      }
      return const Left(ServerFailure());
    } on Exception catch (e) {
      return Left(ServerFailure(
        message: e.toString(),
      ));
    }
  }

  @override
  Future<Either<Failure, InvitationTicketEntity>>
      generateFriendlyInvitationTempCode() async {
    try {
      final result = await datasource.generateFriendlyInvitationTempCode();
      if (result['code'] != null && result['code'] != '') {
        final invitationTicket = InvitationTicketEntity.fromJson(result);
        return Right(invitationTicket);
      }
      return const Left(ServerFailure());
    } on Exception catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, InvitationTicketEntity>>
      validateFriendlyInvitationTempCode(String code) async {
    try {
      final result = await datasource.validateFriendlyInvitationTempCode(code);
      if (result['data'] != null) {
        final invitationTicket =
            InvitationTicketEntity.fromJson(result['data']);
        return Right(invitationTicket);
      }
      return const Left(ServerFailure());
    } on Exception catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> blockPlayer(
      // Retorno alterado para bool
      BlockPlayerParamsEntity params) async {
    try {
      // Assumindo que o datasource.blockPlayer agora retorna Future<bool>
      final result = await datasource.blockPlayer(params);
      return Right(result);
    } on Exception catch (e) {
      // Você pode querer um tipo de falha mais específico ou logar o erro
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> unblockPlayer(int blockId) async {
    try {
      final result = await datasource.unblockPlayer(blockId);
      return Right(result);
    } on Exception catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> reportPlayer(
      // Retorno alterado para bool
      ReportPlayerParamsEntity params) async {
    try {
      final result = await datasource.reportPlayer(params);
      return Right(result);
    } on Exception catch (e) {
      // Você pode querer um tipo de falha mais específico ou logar o erro
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<BlockedPlayerEntity>>> getBlockedPlayersList(
      int userId) async {
    try {
      final result = await datasource.getBlockedPlayersList(userId);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on Exception catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}
