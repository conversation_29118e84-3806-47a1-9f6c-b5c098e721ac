import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:quycky/app/features/game/domain/dto/game_message_dto.dart';
import 'package:quycky/core/enumerators/egame_action.dart';
import 'package:quycky/app/features/user/presenter/store/user_store.dart';
import 'package:quycky/app/services/remote_config/remote_config_store.dart';
import 'package:quycky/app/services/game_socket/game_socket_store.dart';
import 'package:quycky/core/entities/abs_mappable.dart';
import 'package:quycky/core/utils/app_env.dart';
import 'package:web_socket_channel/io.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

typedef GameSocketMessageHandler = void Function(GameMessageDTO message);

class GameSocketService {
  final GameSocketStore _socketStore;
  final UserStore _userStore;

  WebSocketChannel? _channel;
  String _userToken = '';
  final Map<EGameAction, List<GameSocketMessageHandler>> _messageHandlers = {};
  final List<VoidCallback> _connectionHandlers = [];
  final List<VoidCallback> _disconnectionHandlers = [];
  Timer? _reconnectTimer;
  bool _shouldReconnect = true;
  int _reconnectAttempts = 0;
  static const Duration _initialReconnectDelay = Duration(seconds: 3);
  static const Duration _maxReconnectDelay = Duration(seconds: 30);
  Timer? _heartbeatTimer;
  static const Duration _heartbeatInterval = Duration(seconds: 30);

  GameSocketService(this._socketStore, this._userStore) {
    _userStore.observer(
      onState: (state) {
        if (state.user.id.isNotEmpty && !isConnected) {
          _shouldReconnect =
              true; // Sempre habilita reconexão quando usuário está logado
          connect();
        } else if (state.user.id.isEmpty && isConnected) {
          disconnect(); // Desconecta sem reconexão quando usuário faz logout
        }
      },
    );
  }

  bool get isConnected => _socketStore.state.isConnected;
  bool get isConnecting => _socketStore.state.isConnecting;
  String get connectionId => _socketStore.state.connectionId;

  /// Força reconexão imediata (útil para recuperar de estados inconsistentes)
  void forceReconnect() {
    debugPrint('GameSocket: Force reconnect requested');
    _shouldReconnect = true;
    _reconnectAttempts = 0;
    disconnect(shouldReconnect: true);
    connect();
  }

  /// Habilita reconexão automática permanente
  void enablePersistentReconnection() {
    _shouldReconnect = true;
    debugPrint('GameSocket: Persistent reconnection enabled');
  }

  /// Verifica e garante que a conexão está ativa (chama se necessário)
  void ensureConnection() {
    if (_userStore.state.user.id.isNotEmpty && !isConnected && !isConnecting) {
      debugPrint('GameSocket: Ensuring connection - reconnecting...');
      _shouldReconnect = true;
      connect();
    }
  }

  /// Retorna informações de status da conexão para debug
  Map<String, dynamic> getConnectionStatus() {
    return {
      'isConnected': isConnected,
      'isConnecting': isConnecting,
      'shouldReconnect': _shouldReconnect,
      'reconnectAttempts': _reconnectAttempts,
      'connectionId': connectionId,
      'userLoggedIn': _userStore.state.user.id.isNotEmpty,
      'hasHeartbeat': _heartbeatTimer?.isActive ?? false,
    };
  }

  // Adiciona handler para tipos específicos de mensagem
  void addMessageHandler(EGameAction action, GameSocketMessageHandler handler) {
    _messageHandlers.putIfAbsent(action, () => []).add(handler);
  }

  // Remove handler para tipos específicos de mensagem
  void removeMessageHandler(
      EGameAction action, GameSocketMessageHandler handler) {
    _messageHandlers[action]?.remove(handler);
    if (_messageHandlers[action]?.isEmpty == true) {
      _messageHandlers.remove(action);
    }
  }

  // Adiciona handler para conexão estabelecida
  void addConnectionHandler(VoidCallback handler) {
    _connectionHandlers.add(handler);
  }

  // Remove handler de conexão
  void removeConnectionHandler(VoidCallback handler) {
    _connectionHandlers.remove(handler);
  }

  // Adiciona handler para desconexão
  void addDisconnectionHandler(VoidCallback handler) {
    _disconnectionHandlers.add(handler);
  }

  // Remove handler de desconexão
  void removeDisconnectionHandler(VoidCallback handler) {
    _disconnectionHandlers.remove(handler);
  }

  Future<void> connect() async {
    if (isConnected || isConnecting) return;

    try {
      _socketStore.setConnectionState(SocketConnectionState.connecting);
      _userToken = await _userStore.getToken();

      if (_userToken.isEmpty) {
        throw Exception('User token is empty');
      }

      _channel = _createWebSocketChannel();
      await _channel!.ready;

      _socketStore.setConnectionState(SocketConnectionState.connected);
      _reconnectAttempts = 0;

      _configureSocket();
      _startHeartbeat();
      _notifyConnectionHandlers();

      debugPrint('GameSocket: Connected successfully');
    } catch (e) {
      _socketStore.setSocketError('Connection failed: $e');
      debugPrint('GameSocket: Connection failed - $e');
      _scheduleReconnect();
    }
  }

  void disconnect({bool shouldReconnect = false}) {
    _shouldReconnect = shouldReconnect;
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
    _stopHeartbeat();

    try {
      _channel?.sink.close();
    } catch (e) {
      debugPrint('GameSocket: Error closing channel - $e');
    }

    _channel = null;
    _socketStore.setConnectionState(SocketConnectionState.disconnected);
    _notifyDisconnectionHandlers();

    debugPrint('GameSocket: Disconnected (shouldReconnect: $shouldReconnect)');
  }

  void sendMessage(AbsMappable data) {
    if (!isConnected) {
      debugPrint('GameSocket: Cannot send message - not connected');
      return;
    }

    try {
      _channel!.sink.add(data.toJson());
    } catch (e) {
      debugPrint('GameSocket: Error sending message - $e');
      _socketStore.setSocketError('Failed to send message: $e');
    }
  }

  GameMessageDTO createMessage(EGameAction action,
      {AbsMappable? data, String? roomName}) {
    final res = data?.toMap() ?? roomName;
    return GameMessageDTO(
      authorization: _userToken,
      action: action,
      connectionId: connectionId,
      data: res,
    );
  }

  WebSocketChannel _createWebSocketChannel() {
    if (kIsWeb) {
      throw UnimplementedError('Web socket not implemented for web');
    } else {
      RemoteConfigStore remoteConfigStore = Modular.get<RemoteConfigStore>();
      final socketUrl = AppEnv.mode == EAppRunMode.production
          ? remoteConfigStore.state.productionGameServerUrl
          : remoteConfigStore.state.developmentGameServerUrl;

      return IOWebSocketChannel.connect(
        socketUrl,
        headers: {
          "authorization": _userToken,
          "identifier": _userStore.state.user.identifier
        },
      );
    }
  }

  void _configureSocket() {
    _channel!.stream.listen(
      (message) {
        _onMessageReceived(message);
      },
      onDone: () {
        debugPrint('GameSocket: Connection closed');
        _stopHeartbeat();
        _socketStore.setConnectionState(SocketConnectionState.disconnected);
        _notifyDisconnectionHandlers();

        // Sempre reconecta quando a conexão é perdida (exceto se explicitamente desabilitado)
        if (_userStore.state.user.id.isNotEmpty) {
          _shouldReconnect = true;
          _scheduleReconnect();
        }
      },
      onError: (error) {
        debugPrint('GameSocket: Stream error - $error');
        _stopHeartbeat();
        _socketStore.setSocketError('Stream error: $error');
        _notifyDisconnectionHandlers();

        // Sempre reconecta em caso de erro (exceto se explicitamente desabilitado)
        if (_userStore.state.user.id.isNotEmpty) {
          _shouldReconnect = true;
          _scheduleReconnect();
        }
      },
      cancelOnError: true,
    );
  }

  void _onMessageReceived(String message) {
    try {
      final serverMessage = GameMessageDTO.fromJson(message);

      // Atualiza connectionId se recebido
      if (serverMessage.connectionId.isNotEmpty) {
        _socketStore.setConnectionId(serverMessage.connectionId);
      }

      // Notifica handlers específicos para esta ação
      final handlers = _messageHandlers[serverMessage.action];
      if (handlers != null) {
        for (final handler in handlers) {
          handler(serverMessage);
        }
      }
    } catch (e) {
      debugPrint('GameSocket: Error parsing message - $e');
    }
  }

  void _scheduleReconnect() {
    if (!_shouldReconnect) {
      debugPrint('GameSocket: Reconnect disabled');
      return;
    }

    _reconnectAttempts++;

    // Implementa backoff exponencial com limite máximo
    final delay = Duration(
        seconds: (_initialReconnectDelay.inSeconds *
                (1 << (_reconnectAttempts - 1).clamp(0, 4)))
            .clamp(_initialReconnectDelay.inSeconds,
                _maxReconnectDelay.inSeconds));

    debugPrint(
        'GameSocket: Scheduling reconnect attempt #$_reconnectAttempts in ${delay.inSeconds}s');

    _reconnectTimer = Timer(delay, () {
      debugPrint('GameSocket: Attempting reconnect #$_reconnectAttempts');
      connect();
    });
  }

  void _notifyConnectionHandlers() {
    for (final handler in _connectionHandlers) {
      try {
        handler();
      } catch (e) {
        debugPrint('GameSocket: Error in connection handler - $e');
      }
    }
  }

  void _notifyDisconnectionHandlers() {
    for (final handler in _disconnectionHandlers) {
      try {
        handler();
      } catch (e) {
        debugPrint('GameSocket: Error in disconnection handler - $e');
      }
    }
  }

  void _startHeartbeat() {
    _stopHeartbeat();
    _heartbeatTimer = Timer.periodic(_heartbeatInterval, (timer) {
      if (isConnected) {
        try {
          // Envia uma mensagem de connect para manter a conexão viva
          sendMessage(createMessage(EGameAction.connect));
          debugPrint('GameSocket: Heartbeat sent');
        } catch (e) {
          debugPrint('GameSocket: Heartbeat failed - $e');
          // Se o heartbeat falhar, força reconexão
          _scheduleReconnect();
        }
      } else {
        _stopHeartbeat();
      }
    });
  }

  void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  void dispose() {
    _shouldReconnect = false;
    _reconnectTimer?.cancel();
    _stopHeartbeat();
    disconnect();
    _messageHandlers.clear();
    _connectionHandlers.clear();
    _disconnectionHandlers.clear();
  }
}
