import 'package:flutter_triple/flutter_triple.dart';

enum SocketConnectionState {
  disconnected,
  connecting,
  connected,
  error,
}

class GameSocketStoreEntity {
  final SocketConnectionState connectionState;
  final String connectionId;
  final String? errorMessage;
  final DateTime? lastConnectedAt;
  final DateTime? lastDisconnectedAt;

  const GameSocketStoreEntity({
    this.connectionState = SocketConnectionState.disconnected,
    this.connectionId = '',
    this.errorMessage,
    this.lastConnectedAt,
    this.lastDisconnectedAt,
  });

  GameSocketStoreEntity copyWith({
    SocketConnectionState? connectionState,
    String? connectionId,
    String? errorMessage,
    DateTime? lastConnectedAt,
    DateTime? lastDisconnectedAt,
  }) {
    return GameSocketStoreEntity(
      connectionState: connectionState ?? this.connectionState,
      connectionId: connectionId ?? this.connectionId,
      errorMessage: errorMessage ?? this.errorMessage,
      lastConnectedAt: lastConnectedAt ?? this.lastConnectedAt,
      lastDisconnectedAt: lastDisconnectedAt ?? this.lastDisconnectedAt,
    );
  }

  bool get isConnected => connectionState == SocketConnectionState.connected;
  bool get isConnecting => connectionState == SocketConnectionState.connecting;
  bool get hasError => connectionState == SocketConnectionState.error;
}

class GameSocketStore extends Store<GameSocketStoreEntity> {
  GameSocketStore() : super(const GameSocketStoreEntity());

  void setConnectionState(SocketConnectionState state) {
    DateTime? lastConnectedAt = state == SocketConnectionState.connected
        ? DateTime.now()
        : this.state.lastConnectedAt;

    DateTime? lastDisconnectedAt = state == SocketConnectionState.disconnected
        ? DateTime.now()
        : this.state.lastDisconnectedAt;

    update(
        this.state.copyWith(
              connectionState: state,
              lastConnectedAt: lastConnectedAt,
              lastDisconnectedAt: lastDisconnectedAt,
              errorMessage: state == SocketConnectionState.error
                  ? null
                  : this.state.errorMessage,
            ),
        force: true);
  }

  void setConnectionId(String connectionId) {
    update(state.copyWith(connectionId: connectionId), force: true);
  }

  void setSocketError(String errorMessage) {
    update(
        state.copyWith(
          connectionState: SocketConnectionState.error,
          errorMessage: errorMessage,
          lastDisconnectedAt: DateTime.now(),
        ),
        force: true);
  }

  void clearError() {
    update(
        state.copyWith(
          errorMessage: null,
        ),
        force: true);
  }

  void reset() {
    update(const GameSocketStoreEntity(), force: true);
  }
}
