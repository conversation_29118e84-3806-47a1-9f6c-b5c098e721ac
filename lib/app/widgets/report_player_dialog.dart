import 'package:flutter/material.dart';
import './confirmation_dialog.dart'; // Importa o ConfirmationDialog base

class ReportPlayerDialog extends StatelessWidget {
  final VoidCallback onBlockPlayer;
  final VoidCallback onReturn;

  const ReportPlayerDialog({
    super.key,
    required this.onBlockPlayer,
    required this.onReturn,
  });

  @override
  Widget build(BuildContext context) {
    return ConfirmationDialog(
      title: "REPORT SUBMITTED",
      message:
          "This user has been successfully reported. We monitor reports from multiple players to ensure community safety.",
      primaryActionLabel: "BLOCK PLAYER",
      onPrimaryAction: () {
        onBlockPlayer();
        Navigator.of(context).pop();
      },
      secondaryActionLabel: "RETURN",
      onSecondaryAction: () {
        onReturn();
        // Fecha o diálogo ao retornar
        Navigator.of(context).pop();
      },
    );
  }
}

/*
// Exemplo de como usar o ReportPlayerDialog:
//
// void _showReportConfirmation(BuildContext context) {
//   showDialog(
//     context: context,
//     barrierDismissible: false,
//     builder: (BuildContext dialogContext) {
//       return ReportPlayerDialog(
//         onBlockPlayer: () {
//           print("Usuário será bloqueado.");
//           // Adicionar lógica para bloquear o jogador
//           // Pode fechar este dialog e abrir o PlayerBlockedDialog, por exemplo
//           Navigator.of(dialogContext).pop(); 
//           // _showPlayerBlockedConfirmation(context); // Exemplo
//         },
//         onReturn: () {
//           print("Retornando...");
//         },
//       );
//     },
//   );
// }
*/
