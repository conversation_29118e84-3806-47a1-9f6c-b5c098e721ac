import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:quycky/app/features/user/presenter/controllers/user_controller.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/button.dart';
import 'package:quycky/app/widgets/custom_image.dart';
import 'package:quycky/core/utils/interval_utils.dart';
import 'package:quycky/core/utils/social_sign_in.dart';

class SocialLoginButtons extends StatefulWidget {
  final void Function()? callback;
  const SocialLoginButtons({super.key, this.callback});

  @override
  _SocialLoginButtonsState createState() => _SocialLoginButtonsState();
}

class _SocialLoginButtonsState extends State<SocialLoginButtons> {
  final _controller = Modular.get<UserController>();
  final SocialSignIn _socialSignIn = SocialSignIn();

  Widget getButtonChild({String social = 'google'}) {
    return SizedBox(
      height: 28,
      child: SizedBox(
        width: 215,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  CustomImage(
                    'assets/img/png/${social.toLowerCase()}_logo.png',
                    width: 25,
                  ),
                  SizedBox(
                    width: social == 'google' ? 10 : 18,
                  ),
                  Text(
                    'SIGN IN WITH ${social.toUpperCase()}',
                    style: buttonDefaultTextStyle(Colors.black, fontSize: 10),
                  )
                ]),
          ],
        ),
      ),
    );
  }

  void handleGoogleSignIn() async {
    try {
      UserCredential? signInResult = await _socialSignIn.signInWithGoogle();
      final platformLogin =
          await _controller.doLoginWithGoogle(signInResult?.user?.uid ?? '');
      if (platformLogin == null) {
        final res = await _controller.updateUser(
            signInResult?.user?.displayName ??
                _controller.userNameTextEditingController.text,
            googleId: signInResult?.user?.uid);
        final photoURL = signInResult?.user?.photoURL ?? '';
        if (photoURL.isNotEmpty) {
          _controller.updateUserImageFromURL(photoURL);
        }
      }
      setTimeout(
          callback: widget.callback, duration: const Duration(seconds: 2));
    } catch (e) {
      print(
          '==>${e.toString() == '[firebase_auth/web-context-canceled] The web operation was canceled by the user.'}');
    }
  }

  void handleAppleSingIn() async {
    try {
      UserCredential signInResult = await _socialSignIn.signInWithApple();
      final platformLogin =
          await _controller.doLoginWithApple(signInResult.user?.uid ?? '');
      if (platformLogin == null) {
        final res = await _controller.updateUser(
            signInResult.user?.displayName ??
                _controller.userNameTextEditingController.text,
            appleId: signInResult.user?.uid);
      }
      setTimeout(
          callback: widget.callback, duration: const Duration(seconds: 1));
    } catch (e) {
      print(
          '==>${e.toString() == '[firebase_auth/web-context-canceled] The web operation was canceled by the user.'}');
    }
  }

  Widget getButton(Function() onPressed, String label,
      {Color textColor = Colors.white,
      Color color = CustomColors.button,
      Color borderColor = CustomColors.button,
      bool visible = true,
      bool outlined = false,
      Widget? child}) {
    return Visibility(
      visible: visible,
      child: SizedBox(
        width: double.infinity,
        child: Button(
            onPressed: onPressed,
            outlined: outlined,
            autoSized: true,
            text: label,
            textColor: textColor,
            color: color,
            borderColor: borderColor,
            child: child),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        getButton(
          handleGoogleSignIn,
          'GOOGLE',
          color: Colors.white,
          borderColor: Colors.white,
          textColor: CustomColors.button,
          child: getButtonChild(),
        ),
        const SizedBox(height: 18),
        getButton(
          handleAppleSingIn,
          'APPLE',
          color: Colors.white,
          borderColor: Colors.white,
          textColor: CustomColors.button,
          child: getButtonChild(social: 'apple'),
        )
      ],
    );
  }
}
